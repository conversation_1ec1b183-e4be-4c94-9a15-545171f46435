using System.ComponentModel.DataAnnotations;

namespace ParkingBoothApi.Models
{
    public class Vehicle
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        [StringLength(20)]
        public string LicensePlate { get; set; } = string.Empty;
        
        [Required]
        public DateTime EntryTime { get; set; }
        
        public DateTime? ExitTime { get; set; }
        
        [StringLength(10)]
        public string? ParkingSpot { get; set; }
        
        [Required]
        public VehicleType VehicleType { get; set; }
        
        [StringLength(500)]
        public string? ImageUrl { get; set; }
        
        [Required]
        public VehicleStatus Status { get; set; } = VehicleStatus.Parked;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public virtual ICollection<ParkingTransaction> Transactions { get; set; } = new List<ParkingTransaction>();
    }
    
    public enum VehicleType
    {
        Car = 1,
        Motorcycle = 2,
        Truck = 3
    }
    
    public enum VehicleStatus
    {
        Parked = 1,
        Exited = 2,
        PaymentPending = 3
    }
}
