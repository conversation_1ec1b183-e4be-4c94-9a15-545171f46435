using Microsoft.AspNetCore.Mvc;
using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;

namespace ParkingBoothApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EntranceController : ControllerBase
    {
        private readonly IEntranceService _entranceService;
        private readonly ILogger<EntranceController> _logger;

        public EntranceController(IEntranceService entranceService, ILogger<EntranceController> logger)
        {
            _entranceService = entranceService;
            _logger = logger;
        }

        /// <summary>
        /// 车辆检测 - 当车辆到达入口时调用
        /// </summary>
        [HttpPost("detect")]
        public async Task<ActionResult<VehicleDetectionDto>> DetectVehicle([FromBody] VehicleDetectionDto detection)
        {
            try
            {
                var result = await _entranceService.ProcessVehicleDetectionAsync(detection);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "车辆检测处理失败");
                return StatusCode(500, new { message = "车辆检测处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 人工操作 - 岗亭人员处理车辆进场
        /// </summary>
        [HttpPost("manual-action")]
        public async Task<ActionResult<EntryProcessResultDto>> ProcessManualAction([FromBody] ManualEntryActionDto actionDto)
        {
            try
            {
                var result = await _entranceService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "人工操作处理失败");
                return StatusCode(500, new { message = "人工操作处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 刷卡进场
        /// </summary>
        [HttpPost("card-entry")]
        public async Task<ActionResult<EntryProcessResultDto>> ProcessCardEntry([FromBody] CardEntryDto cardDto)
        {
            try
            {
                var result = await _entranceService.ProcessCardEntryAsync(cardDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷卡进场处理失败");
                return StatusCode(500, new { message = "刷卡进场处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 道闸控制
        /// </summary>
        [HttpPost("barrier-control")]
        public async Task<ActionResult<bool>> ControlBarrier([FromBody] BarrierControlDto controlDto)
        {
            try
            {
                var result = await _entranceService.ControlBarrierAsync(controlDto);
                return Ok(new { success = result, message = result ? "道闸控制成功" : "道闸控制失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "道闸控制失败");
                return StatusCode(500, new { message = "道闸控制失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取待处理通知
        /// </summary>
        [HttpGet("notifications")]
        public async Task<ActionResult<List<EntryNotificationDto>>> GetPendingNotifications([FromQuery] string? entranceId = null)
        {
            try
            {
                var notifications = await _entranceService.GetPendingNotificationsAsync(entranceId);
                return Ok(notifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取通知失败");
                return StatusCode(500, new { message = "获取通知失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取入口状态
        /// </summary>
        [HttpGet("status")]
        public async Task<ActionResult<List<EntranceStatusDto>>> GetEntranceStatus()
        {
            try
            {
                var status = await _entranceService.GetEntranceStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取入口状态失败");
                return StatusCode(500, new { message = "获取入口状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取入场统计
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<EntryStatisticsDto>> GetEntryStatistics([FromQuery] DateTime? date = null)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                var statistics = await _entranceService.GetEntryStatisticsAsync(targetDate);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取入场统计失败");
                return StatusCode(500, new { message = "获取入场统计失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 重新抓拍
        /// </summary>
        [HttpPost("recapture")]
        public async Task<ActionResult<EntryProcessResultDto>> Recapture([FromBody] RecaptureRequestDto request)
        {
            try
            {
                var actionDto = new ManualEntryActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = Models.EntryAction.Recapture,
                    OperatorId = request.OperatorId,
                    Reason = request.Reason
                };

                var result = await _entranceService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新抓拍失败");
                return StatusCode(500, new { message = "重新抓拍失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 车牌修改
        /// </summary>
        [HttpPost("modify-plate")]
        public async Task<ActionResult<EntryProcessResultDto>> ModifyLicensePlate([FromBody] ModifyPlateRequestDto request)
        {
            try
            {
                var actionDto = new ManualEntryActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = Models.EntryAction.ModifyInfo,
                    ModifiedLicensePlate = request.NewLicensePlate,
                    OperatorId = request.OperatorId,
                    Reason = "车牌修改"
                };

                var result = await _entranceService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "车牌修改失败");
                return StatusCode(500, new { message = "车牌修改失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 车型修改
        /// </summary>
        [HttpPost("modify-vehicle-type")]
        public async Task<ActionResult<EntryProcessResultDto>> ModifyVehicleType([FromBody] ModifyVehicleTypeRequestDto request)
        {
            try
            {
                var actionDto = new ManualEntryActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = Models.EntryAction.ModifyInfo,
                    ModifiedVehicleType = request.NewVehicleType,
                    OperatorId = request.OperatorId,
                    Reason = "车型修改"
                };

                var result = await _entranceService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "车型修改失败");
                return StatusCode(500, new { message = "车型修改失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 手动放行
        /// </summary>
        [HttpPost("manual-release")]
        public async Task<ActionResult<EntryProcessResultDto>> ManualRelease([FromBody] ManualReleaseRequestDto request)
        {
            try
            {
                var actionDto = new ManualEntryActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = Models.EntryAction.ManualRelease,
                    OperatorId = request.OperatorId,
                    Reason = request.Reason
                };

                var result = await _entranceService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动放行失败");
                return StatusCode(500, new { message = "手动放行失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 拒绝进场
        /// </summary>
        [HttpPost("reject")]
        public async Task<ActionResult<EntryProcessResultDto>> RejectEntry([FromBody] RejectEntryRequestDto request)
        {
            try
            {
                var actionDto = new ManualEntryActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = Models.EntryAction.Reject,
                    OperatorId = request.OperatorId,
                    Reason = request.Reason
                };

                var result = await _entranceService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "拒绝进场失败");
                return StatusCode(500, new { message = "拒绝进场失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 手动关闸
        /// </summary>
        [HttpPost("close-barrier")]
        public async Task<ActionResult<bool>> CloseBarrier([FromBody] CloseBarrierRequestDto request)
        {
            try
            {
                var controlDto = new BarrierControlDto
                {
                    EntranceId = request.EntranceId,
                    Action = Models.BarrierAction.Close,
                    OperatorId = request.OperatorId,
                    Reason = request.Reason
                };

                var result = await _entranceService.ControlBarrierAsync(controlDto);
                return Ok(new { success = result, message = "道闸已关闭" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动关闸失败");
                return StatusCode(500, new { message = "手动关闸失败", error = ex.Message });
            }
        }
    }

    // 请求 DTO 类
    public class ModifyPlateRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string NewLicensePlate { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
    }

    public class ModifyVehicleTypeRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public Models.VehicleType NewVehicleType { get; set; }
        public string OperatorId { get; set; } = string.Empty;
    }

    public class ManualReleaseRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class RejectEntryRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class CloseBarrierRequestDto
    {
        public string EntranceId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }
}
