# API 集成文档

## 概述

本文档描述了停车收费系统前端与后端API的集成方式。系统使用基于HTTP的RESTful API进行通信，主要包括认证、车辆管理、收费管理等功能。

## API基础信息

- **基础URL**: `http://10.0.0.3:9001/yard`
- **开发环境**: `http://10.0.0.3:9001/yard`
- **生产环境**: `/yard`（相对路径）
- **超时设置**: 60秒（开发环境）/ 30秒（生产环境）
- **内容类型**: `application/json;charset=UTF-8`

## 认证机制

系统支持两种认证方式：

### 1. 直接登录

```
POST /auth/login
```

**请求参数**:
```json
{
  "username": "用户名",
  "password": "密码"
}
```

**响应示例**:
```json
{
  "status": 1,
  "msg": "登录成功",
  "data": {
    "token": "认证令牌"
  }
}
```

### 2. OAuth2登录

```
POST /oauth/token
```

**请求参数**:
```
grant_type=password
client_id=yard
client_secret=yard-8888
username=用户名
password=密码
```

**响应示例**:
```json
{
  "access_token": "认证令牌",
  "token_type": "bearer",
  "expires_in": 3600,
  "scope": "all"
}
```

### 认证头部

所有需要认证的API请求都需要在HTTP头部包含以下字段：

```
accessToken: 认证令牌
```

## 主要API端点

### 用户管理

#### 获取用户信息
```
POST /user/findUserByName
```

**请求参数**:
```json
{
  "username": "用户名"
}
```

#### 修改密码
```
POST /user/updatePwd
```

**请求参数**:
```json
{
  "oldPassword": "旧密码",
  "newPassword": "新密码"
}
```

### 首页统计

#### 获取统计数据
```
POST /home/<USER>/sumInfoReport
```

**响应示例**:
```json
{
  "status": 1,
  "data": {
    "totalAmount": 1000,
    "totalVehicles": 50,
    "currentOccupancy": 20,
    "availableSpaces": 30
  }
}
```

### 车辆管理

#### 获取车辆进出记录
```
POST /report/getCarNumInfo
```

**请求参数**:
```json
{
  "startDate": "2023-01-01",
  "endDate": "2023-01-31",
  "plateNumber": "粤B12345",
  "pageNum": 1,
  "pageSize": 10
}
```

#### 获取场内车辆
```
POST /report/getCarInInfo
```

**请求参数**:
```json
{
  "plateNumber": "粤B12345",
  "pageNum": 1,
  "pageSize": 10
}
```

#### 获取收费明细
```
POST /report/getChargeInfo
```

**请求参数**:
```json
{
  "startDate": "2023-01-01",
  "endDate": "2023-01-31",
  "plateNumber": "粤B12345",
  "pageNum": 1,
  "pageSize": 10
}
```

### 设备控制

#### 控制闸机
```
GET /callCenter/openOrReshoot?channnelMac={mac}&action={action}
```

**参数说明**:
- `mac`: 通道MAC地址
- `action`: 操作类型（0:开闸, 1:补拍, 3:关闸）

#### 获取通道列表
```
POST /channel/findAllInfo
```

## 响应格式

所有API响应都遵循以下格式：

```json
{
  "status": 1,          // 状态码：1=成功，0=失败，401=未授权，2=令牌过期，3=权限不足
  "msg": "操作成功",     // 消息描述
  "data": {             // 响应数据（可选）
    // 具体数据
  }
}
```

## 错误处理

### 常见错误码

- `0`: 一般错误
- `401`: 未授权
- `2`: 令牌过期
- `3`: 权限不足

### 错误响应示例

```json
{
  "status": 401,
  "msg": "登录已过期",
  "data": null
}
```

## 前端集成

### 目录结构

```
src/
├── config/
│   └── api.ts           // API配置
├── services/
│   ├── httpClient.ts    // HTTP客户端
│   ├── authApi.ts       // 认证API
│   └── parkingApi.ts    // 停车系统API
└── stores/
    └── auth.ts          // 认证状态管理
```

### 使用示例

#### 登录

```typescript
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const login = async () => {
  try {
    const success = await authStore.login(username, password)
    if (success) {
      // 登录成功
    }
  } catch (error) {
    // 处理错误
  }
}
```

#### 获取车辆数据

```typescript
import parkingApi from '@/services/parkingApi'

const getVehicles = async () => {
  try {
    const response = await parkingApi.getVehiclesInPark({
      pageNum: 1,
      pageSize: 10
    })
    
    if (response.status === 1) {
      // 处理数据
      const vehicles = response.data.list
    }
  } catch (error) {
    // 处理错误
  }
}
```

## 测试API连接

系统提供了API测试页面，可以通过以下URL访问：

```
/api-test
```

该页面可以测试：
- 基础连接
- 登录认证
- 数据获取
- 配置信息

## 注意事项

1. 所有API请求都需要包含认证令牌（除了登录接口）
2. 处理令牌过期情况，当收到401/2/3状态码时，应自动跳转到登录页面
3. 生产环境中使用相对路径，确保部署在不同环境时不需要修改配置
4. 所有敏感信息（如密码）都应该在传输前进行加密处理

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 确认API服务器是否正常运行
   - 检查防火墙设置

2. **认证失败**
   - 验证用户名和密码
   - 检查令牌是否过期
   - 确认客户端ID和密钥是否正确

3. **数据获取失败**
   - 检查请求参数格式
   - 验证认证令牌
   - 查看服务器日志获取详细错误信息
