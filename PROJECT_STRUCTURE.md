# HONGRUI 停车收费系统 - 项目结构

## 📁 项目目录结构

```
ParkingfeePaymentSystem/
├── README.md                           # 项目主说明文档
├── ParkingfeePaymentSystem.sln        # Visual Studio 解决方案文件
├── PROJECT_STRUCTURE.md               # 项目结构说明（本文件）
│
├── backend/                            # 后端 .NET 7 Web API
│   ├── Controllers/                    # API 控制器
│   ├── DTOs/                          # 数据传输对象
│   ├── Hubs/                          # SignalR 集线器
│   ├── Interfaces/                    # 接口定义
│   ├── Models/                        # 数据模型
│   ├── Services/                      # 业务服务
│   ├── Properties/                    # 项目属性
│   ├── bin/                           # 编译输出目录
│   ├── obj/                           # 编译临时文件
│   ├── Program.cs                     # 程序入口点
│   ├── ParkingBoothApi.csproj        # 项目文件
│   ├── ParkingBoothApi.http          # HTTP 测试文件
│   ├── appsettings.json              # 生产环境配置
│   ├── appsettings.Development.json  # 开发环境配置
│   └── README.md                     # 后端说明文档
│
├── frontend/                          # 前端 Vue 3 应用
│   ├── src/                          # 源代码目录
│   │   ├── components/               # Vue 组件
│   │   ├── views/                    # 页面视图
│   │   ├── router/                   # 路由配置
│   │   ├── stores/                   # Pinia 状态管理
│   │   ├── services/                 # API 服务
│   │   ├── utils/                    # 工具函数
│   │   ├── types/                    # TypeScript 类型定义
│   │   ├── styles/                   # 样式文件
│   │   ├── assets/                   # 静态资源
│   │   ├── App.vue                   # 根组件
│   │   └── main.ts                   # 应用入口
│   │
│   ├── electron/                     # Electron 桌面应用
│   │   ├── assets/                   # Electron 资源
│   │   ├── main.js                   # Electron 主进程
│   │   └── preload.js               # 预加载脚本
│   │
│   ├── public/                       # 公共静态文件
│   ├── dist/                         # 构建输出目录
│   ├── node_modules/                 # Node.js 依赖包
│   │
│   ├── package.json                  # 项目依赖配置
│   ├── package-lock.json            # 依赖版本锁定
│   ├── vite.config.ts               # Vite 构建配置
│   ├── tsconfig.json                # TypeScript 配置
│   ├── tsconfig.app.json            # 应用 TS 配置
│   ├── tsconfig.node.json           # Node.js TS 配置
│   ├── env.d.ts                     # 环境类型定义
│   ├── index.html                   # HTML 模板
│   │
│   ├── README.md                    # 前端说明文档
│   ├── ELECTRON_README.md           # Electron 部署说明
│   └── FEATURES_SUMMARY.md          # 功能总览文档
│
└── docs/                            # 项目文档
    ├── API_Documentation.md        # API 接口文档
    ├── Deployment_Guide.md         # 部署指南
    └── Hardware_Integration_Guide.md # 硬件集成指南
```

## 🎯 核心文件说明

### 后端核心文件
- **Program.cs**: 应用程序启动配置
- **Controllers/**: REST API 端点实现
- **Models/**: 数据库实体模型
- **Services/**: 业务逻辑服务层
- **Hubs/**: SignalR 实时通信

### 前端核心文件
- **src/main.ts**: Vue 应用入口点
- **src/App.vue**: 根组件
- **src/components/**: 可复用组件
- **src/views/**: 页面级组件
- **src/router/**: 路由配置
- **src/stores/**: 状态管理

### 配置文件
- **package.json**: 前端依赖和脚本
- **vite.config.ts**: 构建工具配置
- **tsconfig.json**: TypeScript 编译配置
- **appsettings.json**: 后端应用配置

## 🚀 快速开始

### 后端启动
```bash
cd backend
dotnet restore
dotnet run
```

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### Electron 桌面应用
```bash
cd frontend
npm run electron:dev
```

## 📦 构建部署

### 前端构建
```bash
cd frontend
npm run build
```

### Electron 打包
```bash
cd frontend
npm run electron:build
```

### 后端发布
```bash
cd backend
dotnet publish -c Release
```

## 🔧 开发工具

### 推荐 IDE
- **Visual Studio 2022**: 后端开发
- **VS Code**: 前端开发
- **JetBrains Rider**: 全栈开发

### 必需工具
- **.NET 7 SDK**: 后端运行环境
- **Node.js 18+**: 前端开发环境
- **Git**: 版本控制

## 📝 文档说明

### 保留的核心文档
- **README.md**: 项目总体说明
- **FEATURES_SUMMARY.md**: 功能特性总览
- **ELECTRON_README.md**: 桌面应用部署
- **API_Documentation.md**: 后端API文档
- **Deployment_Guide.md**: 系统部署指南
- **Hardware_Integration_Guide.md**: 硬件集成说明

### 已清理的文档
- 删除了重复的功能模块文档
- 删除了开发过程中的临时文档
- 删除了测试和调试文件
- 删除了无用的示例文件

## 🎨 项目特色

### 技术栈
- **前端**: Vue 3 + TypeScript + Vite
- **后端**: ASP.NET Core 7 + Entity Framework
- **桌面**: Electron 跨平台支持
- **数据库**: SQL Server
- **实时通信**: SignalR

### 架构特点
- **前后端分离**: 独立开发和部署
- **响应式设计**: 适配多种设备
- **模块化组件**: 可复用的UI组件
- **类型安全**: TypeScript 全栈支持

---

**泓睿股份有限公司** © 2025 - HONGRUI 停车收费系统
