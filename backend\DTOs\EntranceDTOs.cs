using ParkingBoothApi.Models;

namespace ParkingBoothApi.DTOs
{
    // 入口处理相关的 DTO
    public class VehicleDetectionDto
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string LicensePlate { get; set; } = string.Empty;
        public VehicleType VehicleType { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
        public double Confidence { get; set; }
        public string EntranceId { get; set; } = string.Empty;
        public VehicleEntryStatus Status { get; set; }
        public List<string> BlockingReasons { get; set; } = new();
    }

    public class EntryDecisionDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public bool CanEnter { get; set; }
        public string Reason { get; set; } = string.Empty;
        public List<string> BlockingReasons { get; set; } = new();
        public VehicleCategory Category { get; set; }
        public decimal? EstimatedFee { get; set; }
        public bool RequiresManualIntervention { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    }

    public class ManualEntryActionDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public EntryAction Action { get; set; }
        public string? ModifiedLicensePlate { get; set; }
        public VehicleType? ModifiedVehicleType { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public DateTime ActionTime { get; set; } = DateTime.UtcNow;
    }

    public class BarrierControlDto
    {
        public string EntranceId { get; set; } = string.Empty;
        public BarrierAction Action { get; set; }
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public DateTime ActionTime { get; set; } = DateTime.UtcNow;
    }

    public class RecaptureRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string EntranceId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class CardEntryDto
    {
        public string CardId { get; set; } = string.Empty;
        public string EntranceId { get; set; } = string.Empty;
        public DateTime SwipeTime { get; set; } = DateTime.UtcNow;
        public string? DetectedLicensePlate { get; set; }
    }

    public class EntryNotificationDto
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public NotificationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public VehicleDetectionDto? VehicleInfo { get; set; }
        public EntryDecisionDto? Decision { get; set; }
        public bool RequiresAction { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsHandled { get; set; }
        public string? HandledBy { get; set; }
        public DateTime? HandledAt { get; set; }
    }

    public class EntryProcessResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public VehicleDto? Vehicle { get; set; }
        public EntryDecisionDto? Decision { get; set; }
        public EntryNotificationDto? Notification { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    }

    public class EntranceStatusDto
    {
        public string EntranceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool IsOnline { get; set; }
        public bool IsBarrierOpen { get; set; }
        public bool IsCameraWorking { get; set; }
        public bool IsCardReaderWorking { get; set; }
        public int QueueLength { get; set; }
        public DateTime LastActivity { get; set; }
        public List<VehicleDetectionDto> PendingVehicles { get; set; } = new();
    }

    public class EntryStatisticsDto
    {
        public DateTime Date { get; set; }
        public int TotalEntries { get; set; }
        public int AutomaticEntries { get; set; }
        public int ManualEntries { get; set; }
        public int CardEntries { get; set; }
        public int RejectedEntries { get; set; }
        public int RecaptureRequests { get; set; }
        public double AverageProcessingTime { get; set; }
        public List<string> TopRejectionReasons { get; set; } = new();
    }
}

namespace ParkingBoothApi.Models
{
    public enum VehicleEntryStatus
    {
        Detected,
        Processing,
        Approved,
        Rejected,
        ManualReview,
        Entered,
        Cancelled
    }

    public enum VehicleCategory
    {
        Temporary,
        Monthly,
        Internal,
        VIP,
        Visitor,
        Blacklisted
    }

    public enum EntryAction
    {
        Allow,
        Reject,
        ManualRelease,
        ModifyInfo,
        Recapture,
        Postpone,
        CloseBarrier
    }

    public enum BarrierAction
    {
        Open,
        Close,
        Reset,
        Emergency
    }

    public enum NotificationType
    {
        VehicleBlocked,
        ManualInterventionRequired,
        SystemAlert,
        EntrySuccess,
        EntryRejected,
        EquipmentFailure,
        NewFeature,
        Alert,
        WorkOrder,
        System,
        Maintenance
    }
}
