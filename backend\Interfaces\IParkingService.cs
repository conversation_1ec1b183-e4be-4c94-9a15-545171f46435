using ParkingBoothApi.DTOs;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Interfaces
{
    public interface IParkingService
    {
        // Vehicle Management
        Task<VehicleDto> RecordVehicleEntryAsync(CreateVehicleEntryDto entryDto);
        Task<ParkingTransactionDto> RecordVehicleExitAsync(VehicleExitDto exitDto);
        Task<VehicleDto?> GetVehicleByIdAsync(string vehicleId);
        Task<VehicleDto?> GetVehicleByLicensePlateAsync(string licensePlate);
        Task<VehicleListResponseDto> GetCurrentVehiclesAsync(VehicleSearchDto searchDto);
        Task<VehicleListResponseDto> SearchVehiclesAsync(VehicleSearchDto searchDto);
        
        // Transaction Management
        Task<ParkingTransactionDto?> GetTransactionByIdAsync(string transactionId);
        Task<TransactionListResponseDto> GetTransactionsAsync(TransactionSearchDto searchDto);
        Task<TransactionListResponseDto> GetPendingTransactionsAsync();
        Task<PaymentResponseDto> ProcessPaymentAsync(ProcessPaymentDto paymentDto);
        Task<FeeCalculationDto> CalculateParkingFeeAsync(string vehicleId);
        
        // Statistics and Reporting
        Task<SystemStatistics> GetTodayStatisticsAsync();
        Task<SystemStatistics> GetStatisticsForDateAsync(DateTime date);
        Task<List<SystemStatistics>> GetStatisticsForPeriodAsync(DateTime startDate, DateTime endDate);
        Task<Dictionary<string, object>> GenerateReportAsync(string reportType, DateTime date);
        
        // Configuration
        Task<ParkingConfiguration> GetConfigurationAsync();
        Task<ParkingConfiguration> UpdateConfigurationAsync(ParkingConfiguration config);
        
        // Capacity Management
        Task<int> GetCurrentOccupancyAsync();
        Task<int> GetAvailableSpotsAsync();
        Task<bool> IsCapacityAvailableAsync();
        
        // Validation
        Task<bool> IsValidLicensePlateAsync(string licensePlate);
        Task<bool> IsVehicleCurrentlyParkedAsync(string licensePlate);
        Task<TimeSpan> GetParkingDurationAsync(string vehicleId);
    }
}
