<template>
  <div class="top-menu-bar-container">
    <div class="unified-top-bar">
    <!-- 左侧区域 -->
    <div class="top-left-section">
      <!-- 入口列表按钮 -->
      <div class="menu-container">
        <button
          @click="toggleChannelList"
          class="menu-button entrance-list-btn"
          :class="{ active: showChannelList }"
        >
          <span class="menu-icon">📋</span>
          <span class="menu-text">入口列表</span>
        </button>
      </div>

      <div class="page-indicator">
        <span class="page-icon">{{ pageIcon }}</span>
        <span class="page-title">{{ pageTitle }}</span>
      </div>
    </div>

    <!-- 中央Logo区域 -->
    <div class="top-center-section">
      <div class="logo-container-center">
        <div class="logo-icon">🅿️</div>
        <div class="logo-content">
          <div class="logo-main">
            <span class="logo-brand">智慧停车收费管理系统</span>
            <span class="logo-version">v1.0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="top-right-section">
      <div class="status-info">
        <div class="time-display-right">
          <div class="current-time-only">{{ currentTime }}</div>
        </div>

        <div class="status-item" @click="openParkingModal">
          <span class="status-label">{{ statusLabel }}:</span>
          <span class="status-value">{{ statusValue }}</span>
        </div>

        <div class="action-buttons">
          <button @click="refreshData" class="action-btn refresh" :disabled="isRefreshing">
            <span class="btn-icon">🔄</span>
            <span class="btn-text">{{ isRefreshing ? '刷新中...' : '刷新' }}</span>
          </button>
          <button @click="goBack" class="action-btn back">
            <span class="btn-icon">🚪</span>
            <span class="btn-text">返回</span>
          </button>
        </div>

        <!-- 用户信息 -->
        <div class="user-container">
          <div
            class="user-info"
            :class="{ active: showUserMenu }"
            @click="toggleUserMenu($event)"
          >
            <div class="user-avatar">
              <span class="avatar-icon">👤</span>
            </div>
            <div class="user-details">
              <span class="username">收费员</span>
            </div>
            <span class="dropdown-arrow" :class="{ active: showUserMenu }">▼</span>
          </div>

          <div class="user-dropdown" :class="{ show: showUserMenu }">
            <!-- 用户信息头部 -->
            <div class="user-dropdown-header">
              <div class="user-avatar-large">
                <span class="avatar-text">aofe</span>
                <span class="dropdown-arrow-user">▼</span>
              </div>
            </div>

            <!-- 离线数据总览 -->
            <div class="offline-data-section">
              <h3 class="section-title">当前班次数据总览</h3>

              <div class="data-grid">
                <div class="data-row">
                  <div class="data-item">
                    <span class="data-label">应收总额</span>
                    <span class="data-value">173MOP</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">免费总额</span>
                    <span class="data-value">0MOP</span>
                  </div>
                </div>

                <div class="data-row">
                  <div class="data-item">
                    <span class="data-label">现金总额</span>
                    <span class="data-value">173MOP</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">抵扣金额</span>
                    <span class="data-value">0MOP</span>
                  </div>
                </div>

                <div class="data-row">
                  <div class="data-item">
                    <span class="data-label">抵扣时长</span>
                    <span class="data-value">0</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">票据抵扣</span>
                    <span class="data-value">0MOP</span>
                  </div>
                </div>

                <div class="data-row">
                  <div class="data-item">
                    <span class="data-label">抵扣张数</span>
                    <span class="data-value">0</span>
                  </div>
                </div>

                <div class="data-row">
                  <div class="data-item">
                    <span class="data-label">总放行</span>
                    <span class="data-value">5次</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">正常放行</span>
                    <span class="data-value">2次</span>
                  </div>
                </div>

                <div class="data-row">
                  <div class="data-item">
                    <span class="data-label">异常放行</span>
                    <span class="data-value">3次</span>
                  </div>
                  <div class="data-item">
                    <span class="data-label">免费放行</span>
                    <span class="data-value">0次</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 功能按钮区域 -->
            <div class="function-buttons">
              <div class="button-row">
                <button class="function-btn primary" @click="viewMonitorSettings">
                  <span class="btn-icon">🖥️</span>
                  <span>监控设置</span>
                </button>
                <button class="function-btn secondary" @click="viewEntrySettings">
                  <span class="btn-icon">🚪</span>
                  <span>进出口设置</span>
                </button>
              </div>

              <div class="button-row">
                <button class="function-btn important" @click="viewChargeDetails">
                  <span class="btn-icon">💰</span>
                  <span>收费明细</span>
                </button>
                <button class="function-btn primary" @click="viewPlateModification">
                  <span class="btn-icon">�</span>
                  <span>车牌修改</span>
                </button>
              </div>

              <div class="button-row">
                <button class="function-btn warning" @click="viewVehicleEntry">
                  <span class="btn-icon">�</span>
                  <span>车辆进入明细</span>
                </button>
                <button class="function-btn secondary" @click="viewPasswordChange">
                  <span class="btn-icon">🔒</span>
                  <span>修改密码</span>
                </button>
              </div>

              <div class="button-row">
                <button class="function-btn important" @click="viewShiftHandover">
                  <span class="btn-icon">🔄</span>
                  <span>交班</span>
                </button>
                <button class="function-btn warning" @click="handleLogout">
                  <span class="btn-icon">🚪</span>
                  <span>注销</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 美化的注销确认弹窗 -->
  <div v-if="showLogoutModal" class="logout-modal-overlay" @click="cancelLogout">
    <div class="logout-modal" @click.stop>
      <div class="logout-modal-header">
        <div class="logout-icon">
          <span class="logout-icon-symbol">🚪</span>
        </div>
        <h3 class="logout-title">确认注销</h3>
      </div>

      <div class="logout-modal-body">
        <p class="logout-message">您确定要注销当前账户吗？</p>
        <p class="logout-submessage">注销后需要重新登录才能继续使用系统</p>
      </div>

      <div class="logout-modal-footer">
        <button class="logout-btn cancel" @click="cancelLogout">
          <span class="btn-icon">❌</span>
          <span>取消</span>
        </button>
        <button class="logout-btn confirm" @click="confirmLogout">
          <span class="btn-icon">✅</span>
          <span>确认注销</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 修改剩余车位弹窗 -->
  <div v-if="showParkingModal" class="parking-modal-overlay" @click="closeParkingModal">
    <div class="parking-modal" @click.stop>
      <div class="parking-modal-header">
        <h3 class="parking-title">修改剩余车位</h3>
        <button class="close-btn" @click="closeParkingModal">✕</button>
      </div>

      <div class="parking-modal-body">
        <div class="parking-table">
          <div class="table-header">
            <div class="header-cell">区域名称</div>
            <div class="header-cell">总车位数</div>
            <div class="header-cell">剩余车位数</div>
            <div class="header-cell">操作</div>
          </div>

          <div class="table-row" v-for="area in parkingAreas" :key="area.id">
            <div class="table-cell area-name">{{ area.name }}</div>
            <div class="table-cell total-spaces">{{ area.totalSpaces }}</div>
            <div class="table-cell remaining-spaces">
              <input
                type="number"
                v-model="area.remainingSpaces"
                :max="area.totalSpaces"
                min="0"
                class="spaces-input"
              />
            </div>
            <div class="table-cell action-cell">
              <button class="modify-btn" @click="modifyAreaSpaces(area)">修改</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 交班确认弹窗 -->
  <div v-if="showShiftHandoverModal" class="shift-modal-overlay" @click="closeShiftHandoverModal">
    <div class="shift-modal" @click.stop>
      <div class="shift-modal-header">
        <h3 class="shift-title">交班确认</h3>
        <button class="close-btn" @click="closeShiftHandoverModal">✕</button>
      </div>

      <div class="shift-modal-body">
        <div class="shift-info">
          <div class="shift-user-info">
            <span class="user-label">当前用户：{{ shiftData.currentUser }}，请确认交班信息！</span>
            <span class="shift-time">上次交班时间：{{ shiftData.lastShiftTime }}</span>
          </div>
        </div>

        <!-- 出口岗亭当前班次数据汇总 -->
        <div class="shift-section">
          <div class="section-header">出口岗亭当前班次数据汇总</div>
          <div class="data-grid">
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">应收金额</span>
                <span class="data-value">{{ shiftData.exitData.receivableAmount }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">现金金额</span>
                <span class="data-value">{{ shiftData.exitData.cashAmount }}</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">免费金额</span>
                <span class="data-value">{{ shiftData.exitData.freeAmount }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">抵扣金额</span>
                <span class="data-value">{{ shiftData.exitData.discountAmount }}</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">抵扣时长</span>
                <span class="data-value">{{ shiftData.exitData.discountTime }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">抵扣张数</span>
                <span class="data-value">{{ shiftData.exitData.discountCount }}</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">实际抵扣</span>
                <span class="data-value">{{ shiftData.exitData.actualDiscount }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">--</span>
                <span class="data-value">--</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">总放行</span>
                <span class="data-value">{{ shiftData.exitData.totalRelease }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">正常放行</span>
                <span class="data-value">{{ shiftData.exitData.normalRelease }}</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">异常放行</span>
                <span class="data-value">{{ shiftData.exitData.abnormalRelease }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">免费放行</span>
                <span class="data-value">{{ shiftData.exitData.freeRelease }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 中央岗亭当前班次数据 -->
        <div class="shift-section">
          <div class="section-header">中央岗亭当前班次数据</div>
          <div class="data-grid">
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">应收金额</span>
                <span class="data-value">{{ shiftData.centralData.receivableAmount }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">现金金额</span>
                <span class="data-value">{{ shiftData.centralData.cashAmount }}</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">免费金额</span>
                <span class="data-value">{{ shiftData.centralData.freeAmount }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">抵扣金额</span>
                <span class="data-value">{{ shiftData.centralData.discountAmount }}</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">抵扣时长</span>
                <span class="data-value">{{ shiftData.centralData.discountTime }}</span>
              </div>
              <div class="data-item">
                <span class="data-label">抵扣张数</span>
                <span class="data-value">{{ shiftData.centralData.discountCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="shift-modal-footer">
          <button class="shift-btn cancel" @click="takeShift">
            取消交班
          </button>
          <button class="shift-btn confirm" @click="confirmShift">
            确认交班
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 进出口设置弹窗 -->
  <div v-if="showEntrySettingsModal" class="entry-settings-modal-overlay" @click="closeEntrySettingsModal">
    <div class="entry-settings-modal" @click.stop>
      <div class="entry-settings-header">
        <h3 class="entry-settings-title">进出口设置</h3>
        <button class="close-btn" @click="closeEntrySettingsModal">✕</button>
      </div>

      <div class="entry-settings-body">
        <!-- 标签页和搜索区域 -->
        <div class="entry-settings-controls">
          <div class="tab-buttons">
            <button
              v-for="tab in ['全部', '入口地点', '出口地点']"
              :key="tab"
              @click="setActiveTab(tab)"
              class="tab-btn"
              :class="{ active: entrySettingsData.activeTab === tab }"
            >
              {{ tab }}
            </button>
          </div>

          <div class="search-and-actions">
            <div class="search-container">
              <input
                v-model="entrySettingsData.searchKeyword"
                @input="searchChannels"
                type="text"
                placeholder="通道名称搜索"
                class="search-input"
              />
            </div>

            <div class="action-buttons">
              <button class="action-btn search">搜索</button>
              <button class="action-btn long-view">一键长视</button>
              <button class="action-btn short-view">一键短视</button>
            </div>
          </div>
        </div>

        <!-- 通道列表表格 -->
        <div class="channels-table">
          <div class="table-header">
            <div class="header-cell channel-name">通道名称</div>
            <div class="header-cell channel-status">通道状态</div>
            <div class="header-cell modal-control">模式控制</div>
            <div class="header-cell distance-control">距离控制</div>
          </div>

          <div class="table-body">
            <div
              v-for="channel in getFilteredChannels()"
              :key="channel.id"
              class="table-row"
            >
              <div class="table-cell channel-name">{{ channel.name }}</div>
              <div class="table-cell channel-status">
                <span class="status-badge active">{{ channel.status }}</span>
              </div>
              <div class="table-cell modal-control">
                <div class="control-buttons">
                  <button
                    class="control-btn"
                    :class="{ active: channel.modalControl === '禁用' }"
                    @click="toggleModalControl(channel)"
                  >
                    禁用
                  </button>
                  <button
                    class="control-btn"
                    :class="{ active: channel.modalControl === '常规' }"
                    @click="toggleModalControl(channel)"
                  >
                    常规
                  </button>
                </div>
              </div>
              <div class="table-cell distance-control">
                <div class="control-buttons">
                  <button
                    class="control-btn"
                    :class="{ active: channel.distanceControl === '启用' }"
                    @click="toggleDistanceControl(channel)"
                  >
                    启用
                  </button>
                  <button
                    class="control-btn"
                    :class="{ active: channel.distanceControl === '禁用' }"
                    @click="toggleDistanceControl(channel)"
                  >
                    禁用
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 监控设置弹窗 -->
  <div v-if="showMonitorSettingsModal" class="monitor-settings-modal-overlay" @click="closeMonitorSettingsModal">
    <div class="monitor-settings-modal" @click.stop>
      <div class="monitor-settings-header">
        <h3 class="monitor-settings-title">监控设置</h3>
        <button class="close-btn" @click="closeMonitorSettingsModal">✕</button>
      </div>

      <div class="monitor-settings-body">
        <!-- 提示信息 -->
        <div class="monitor-notice">
          用於配置當前需要顯示的監控視頻數量及其顯示定位通道 (windows server 系統不支援獨立監控)
        </div>

        <!-- 监控模式选择 -->
        <div class="monitor-modes">
          <button
            v-for="mode in monitorSettingsData.modes"
            :key="mode.value"
            @click="selectMonitorMode(mode.value)"
            class="mode-btn"
            :class="{ active: monitorSettingsData.selectedMode === mode.value }"
          >
            {{ mode.label }}
          </button>

          <!-- 独立窗口模式选项 -->
          <div class="independent-window-option">
            <label class="checkbox-container">
              <input
                type="checkbox"
                v-model="monitorSettingsData.independentWindow"
                @change="toggleIndependentWindow"
                class="checkbox-input"
              />
              <span class="checkbox-label">獨立窗口模式</span>
            </label>
          </div>
        </div>

        <!-- 监控视频显示区域 -->
        <div class="monitor-display-area">
          <!-- 这里可以显示监控视频预览 -->
          <div class="monitor-placeholder">
            <span class="placeholder-text">监控视频显示区域</span>
            <span class="placeholder-mode">当前模式: {{ monitorSettingsData.selectedMode }}</span>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="monitor-settings-footer">
          <button class="monitor-btn cancel" @click="cancelMonitorSettings">
            取消
          </button>
          <button class="monitor-btn confirm" @click="confirmMonitorSettings">
            确定
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 收费明细弹窗 -->
  <div v-if="showChargeDetailsModal" class="charge-details-modal-overlay" @click="closeChargeDetailsModal">
    <div class="charge-details-modal" @click.stop>
      <div class="charge-details-header">
        <h3 class="charge-details-title">
          當前設定文件明細 (不包含當前上述的收費明細數據明細)
        </h3>
        <button class="close-btn" @click="closeChargeDetailsModal">✕</button>
      </div>

      <div class="charge-details-body">
        <!-- 时间筛选区域 -->
        <div class="date-filter-section">
          <div class="date-inputs">
            <div class="date-group">
              <label>开始时间:</label>
              <input
                type="date"
                v-model="chargeDetailsData.dateRange.startDate"
                class="date-input"
              />
              <input
                type="time"
                v-model="chargeDetailsData.dateRange.startTime"
                class="time-input"
              />
            </div>
            <div class="date-separator">-</div>
            <div class="date-group">
              <label>结束时间:</label>
              <input
                type="date"
                v-model="chargeDetailsData.dateRange.endDate"
                class="date-input"
              />
              <input
                type="time"
                v-model="chargeDetailsData.dateRange.endTime"
                class="time-input"
              />
            </div>
          </div>

          <div class="filter-actions">
            <button class="filter-btn search" @click="searchChargeDetails">
              搜索
            </button>
            <button class="filter-btn export" @click="exportChargeDetails">
              导出
            </button>
          </div>
        </div>

        <!-- 收费明细表格 -->
        <div class="charge-details-table">
          <div class="table-header">
            <div class="header-cell plate-number">车牌号码</div>
            <div class="header-cell amount">应收金额</div>
            <div class="header-cell amount">实收金额</div>
            <div class="header-cell amount">优惠金额</div>
            <div class="header-cell discount">优惠类型</div>
            <div class="header-cell datetime">入场时间</div>
            <div class="header-cell datetime">出场时间</div>
            <div class="header-cell payment">付款方式</div>
            <div class="header-cell operator">操作员</div>
            <div class="header-cell status">状态</div>
            <div class="header-cell action">操作</div>
          </div>

          <div class="table-body">
            <div
              v-for="record in chargeDetailsData.records"
              :key="record.id"
              class="table-row"
            >
              <div class="table-cell plate-number">
                {{ record.plateNumber || '--' }}
              </div>
              <div class="table-cell amount">{{ record.receivableAmount }}</div>
              <div class="table-cell amount">{{ record.actualAmount }}</div>
              <div class="table-cell amount">{{ record.discountAmount }}</div>
              <div class="table-cell discount">{{ record.discountType }}</div>
              <div class="table-cell datetime">{{ formatDateTime(record.entryTime) }}</div>
              <div class="table-cell datetime">{{ formatDateTime(record.exitTime) }}</div>
              <div class="table-cell payment">{{ record.paymentMethod }}</div>
              <div class="table-cell operator">{{ record.operator }}</div>
              <div class="table-cell status">
                <span class="status-badge" :class="getStatusColor(record.status)">
                  {{ record.status }}
                </span>
              </div>
              <div class="table-cell action">
                <button class="action-link">详情</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="charge-statistics">
          <div class="stats-item">
            <span class="stats-label">总计 {{ chargeDetailsData.statistics.totalRecords }}条</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">应收金额: {{ chargeDetailsData.statistics.totalReceivable }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">实收金额: {{ chargeDetailsData.statistics.totalActual }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">优惠金额: {{ chargeDetailsData.statistics.totalDiscount }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 车牌修改弹窗 -->
  <div v-if="showPlateModificationModal" class="plate-modification-modal-overlay" @click="closePlateModificationModal">
    <div class="plate-modification-modal" @click.stop>
      <div class="plate-modification-header">
        <h3 class="plate-modification-title">車牌修改</h3>
        <button class="close-btn" @click="closePlateModificationModal">✕</button>
      </div>

      <div class="plate-modification-body">
        <!-- 搜索筛选区域 -->
        <div class="search-filter-section">
          <div class="filter-group">
            <label>车牌号码:</label>
            <input
              type="text"
              v-model="plateModificationData.searchFilters.plateNumber"
              placeholder="请输入车牌号码"
              class="filter-input"
            />
          </div>

          <div class="filter-group">
            <label>时间范围:</label>
            <select
              v-model="plateModificationData.searchFilters.timeRange"
              class="filter-select"
            >
              <option
                v-for="option in plateModificationData.timeRangeOptions"
                :key="option"
                :value="option"
              >
                {{ option }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label>状态:</label>
            <select
              v-model="plateModificationData.searchFilters.status"
              class="filter-select"
            >
              <option
                v-for="option in plateModificationData.statusOptions"
                :key="option"
                :value="option"
              >
                {{ option }}
              </option>
            </select>
          </div>

          <div class="filter-actions">
            <button class="filter-btn search" @click="searchPlateModification">
              搜索
            </button>
            <button class="filter-btn export" @click="exportPlateData">
              导出
            </button>
          </div>
        </div>

        <!-- 监控通道网格 -->
        <div class="monitoring-grid">
          <div
            v-for="channel in plateModificationData.monitoringChannels"
            :key="channel.id"
            class="monitoring-channel"
          >
            <div class="channel-header">
              <span class="channel-name">{{ channel.name }}</span>
              <span class="channel-status" :class="channel.status">{{ channel.status }}</span>
            </div>

            <div class="channel-image">
              <img :src="channel.image" :alt="channel.name" />
              <div class="image-overlay">
                <div class="confidence-badge">
                  置信度: {{ channel.confidence }}%
                </div>
              </div>
            </div>

            <div class="channel-info">
              <div class="plate-info">
                <label>车牌号码:</label>
                <input
                  type="text"
                  v-model="channel.plateNumber"
                  class="plate-input"
                  aria-label="车牌号码输入"
                />
              </div>

              <div class="timestamp-info">
                <span class="timestamp">{{ channel.timestamp }}</span>
              </div>

              <div class="channel-actions">
                <button
                  class="action-btn confirm"
                  @click="confirmPlateModification(channel.id, channel.plateNumber)"
                >
                  确认
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 车辆进入明细弹窗 -->
  <div v-if="showVehicleEntryModal" class="vehicle-entry-modal-overlay" @click="closeVehicleEntryModal">
    <div class="vehicle-entry-modal" @click.stop>
      <div class="vehicle-entry-header">
        <h3 class="vehicle-entry-title">车辆进入明细</h3>
        <button class="close-btn" @click="closeVehicleEntryModal">✕</button>
      </div>

      <div class="vehicle-entry-body">
        <!-- 搜索筛选区域 -->
        <div class="entry-filter-section">
          <div class="filter-row">
            <div class="filter-group">
              <label>开始时间:</label>
              <input
                type="date"
                v-model="vehicleEntryData.searchFilters.startDate"
                class="filter-input date-input"
              />
              <input
                type="time"
                v-model="vehicleEntryData.searchFilters.startTime"
                class="filter-input time-input"
              />
            </div>

            <div class="filter-separator">-</div>

            <div class="filter-group">
              <label>结束时间:</label>
              <input
                type="date"
                v-model="vehicleEntryData.searchFilters.endDate"
                class="filter-input date-input"
              />
              <input
                type="time"
                v-model="vehicleEntryData.searchFilters.endTime"
                class="filter-input time-input"
              />
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-group">
              <label>车牌号码:</label>
              <input
                type="text"
                v-model="vehicleEntryData.searchFilters.plateNumber"
                placeholder="请输入车牌号码"
                class="filter-input"
              />
            </div>

            <div class="filter-group">
              <label>进入方式:</label>
              <select
                v-model="vehicleEntryData.searchFilters.entryMethod"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.entryMethodOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>车辆类型:</label>
              <select
                v-model="vehicleEntryData.searchFilters.vehicleType"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.vehicleTypeOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>付款状态:</label>
              <select
                v-model="vehicleEntryData.searchFilters.paymentStatus"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.paymentStatusOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>进入通道:</label>
              <select
                v-model="vehicleEntryData.searchFilters.entryChannel"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.entryChannelOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>
          </div>

          <div class="filter-actions">
            <div class="action-checkboxes">
              <label class="checkbox-label">
                <input type="checkbox" class="checkbox-input" />
                <span>只显示异常</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" class="checkbox-input" />
                <span>打印报表</span>
              </label>
            </div>

            <div class="action-buttons">
              <button class="filter-btn search" @click="searchVehicleEntry">
                搜索
              </button>
              <button class="filter-btn export" @click="exportVehicleEntry">
                导出
              </button>
            </div>
          </div>
        </div>

        <!-- 车辆进入明细表格 -->
        <div class="vehicle-entry-table">
          <div class="table-header">
            <div class="header-cell plate-number">车牌号码</div>
            <div class="header-cell entry-time">进入时间</div>
            <div class="header-cell entry-method">进入方式</div>
            <div class="header-cell vehicle-type">车辆类型</div>
            <div class="header-cell payment-status">付款状态</div>
            <div class="header-cell entry-channel">进入通道</div>
            <div class="header-cell operator">操作员</div>
            <div class="header-cell remarks">备注</div>
            <div class="header-cell actions">操作</div>
          </div>

          <div class="table-body">
            <div
              v-for="record in vehicleEntryData.records"
              :key="record.id"
              class="table-row"
            >
              <div class="table-cell plate-number">
                {{ record.plateNumber }}
              </div>
              <div class="table-cell entry-time">{{ record.entryTime }}</div>
              <div class="table-cell entry-method">{{ record.entryMethod }}</div>
              <div class="table-cell vehicle-type">{{ record.vehicleType }}</div>
              <div class="table-cell payment-status">
                <span class="status-badge" :class="record.paymentStatus">
                  {{ record.paymentStatus }}
                </span>
              </div>
              <div class="table-cell entry-channel">{{ record.entryChannel }}</div>
              <div class="table-cell operator">{{ record.operator }}</div>
              <div class="table-cell remarks">{{ record.remarks }}</div>
              <div class="table-cell actions">
                <button
                  class="action-link"
                  @click="editVehicleEntry(record.id)"
                >
                  {{ record.actions }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 密码修改弹窗 -->
  <div v-if="showPasswordChangeModal" class="password-change-modal-overlay" @click="closePasswordChangeModal">
    <div class="password-change-modal" @click.stop>
      <div class="password-change-header">
        <h3 class="password-change-title">修改密码</h3>
        <button class="close-btn" @click="closePasswordChangeModal">✕</button>
      </div>

      <div class="password-change-body">
        <div class="password-form">
          <!-- 当前密码 -->
          <div class="form-group">
            <label class="form-label">当前密码</label>
            <div class="password-input-wrapper">
              <input
                :type="passwordChangeData.showCurrentPassword ? 'text' : 'password'"
                v-model="passwordChangeData.currentPassword"
                placeholder="请输入当前密码"
                class="password-input"
                autocomplete="current-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePasswordVisibility('current')"
              >
                {{ passwordChangeData.showCurrentPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
          </div>

          <!-- 新密码 -->
          <div class="form-group">
            <label class="form-label">新密码</label>
            <div class="password-input-wrapper">
              <input
                :type="passwordChangeData.showNewPassword ? 'text' : 'password'"
                v-model="passwordChangeData.newPassword"
                @input="checkPasswordStrength(passwordChangeData.newPassword)"
                placeholder="请输入新密码"
                class="password-input"
                autocomplete="new-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePasswordVisibility('new')"
              >
                {{ passwordChangeData.showNewPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>

            <!-- 密码强度指示器 -->
            <div class="password-strength">
              <div class="strength-bar">
                <div
                  class="strength-fill"
                  :class="{
                    'weak': passwordChangeData.passwordStrength <= 2,
                    'medium': passwordChangeData.passwordStrength === 3 || passwordChangeData.passwordStrength === 4,
                    'strong': passwordChangeData.passwordStrength === 5
                  }"
                  :style="{ width: (passwordChangeData.passwordStrength * 20) + '%' }"
                ></div>
              </div>
              <span class="strength-text">
                {{
                  passwordChangeData.passwordStrength <= 2 ? '弱' :
                  passwordChangeData.passwordStrength <= 4 ? '中' : '强'
                }}
              </span>
            </div>
          </div>

          <!-- 确认新密码 -->
          <div class="form-group">
            <label class="form-label">确认新密码</label>
            <div class="password-input-wrapper">
              <input
                :type="passwordChangeData.showConfirmPassword ? 'text' : 'password'"
                v-model="passwordChangeData.confirmPassword"
                placeholder="请再次输入新密码"
                class="password-input"
                autocomplete="new-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePasswordVisibility('confirm')"
              >
                {{ passwordChangeData.showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>

            <!-- 密码匹配提示 -->
            <div
              v-if="passwordChangeData.confirmPassword"
              class="password-match"
              :class="{
                'match': passwordChangeData.newPassword === passwordChangeData.confirmPassword,
                'mismatch': passwordChangeData.newPassword !== passwordChangeData.confirmPassword
              }"
            >
              {{
                passwordChangeData.newPassword === passwordChangeData.confirmPassword
                  ? '✓ 密码匹配'
                  : '✗ 密码不匹配'
              }}
            </div>
          </div>

          <!-- 密码要求 -->
          <div class="password-requirements">
            <h4 class="requirements-title">密码要求：</h4>
            <ul class="requirements-list">
              <li :class="{ 'met': passwordChangeData.passwordRequirements.minLength }">
                至少8个字符
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasUppercase }">
                包含大写字母
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasLowercase }">
                包含小写字母
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasNumber }">
                包含数字
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasSpecialChar }">
                包含特殊字符
              </li>
            </ul>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="password-change-footer">
          <button class="password-btn cancel" @click="closePasswordChangeModal">
            取消
          </button>
          <button class="password-btn confirm" @click="confirmPasswordChange">
            确认修改
          </button>
        </div>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { notification } from '../utils/notification'

interface Props {
  pageTitle: string
  pageIcon: string
  statusLabel: string
  statusValue: string
  isRefreshing?: boolean
}

withDefaults(defineProps<Props>(), {
  isRefreshing: false
})

const emit = defineEmits<{
  refresh: []
  goBack: []
  toggleChannelList: []
}>()

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const showMenu = ref(false)
const showUserMenu = ref(false)
const showChannelList = ref(false)
const showLogoutModal = ref(false)
const showParkingModal = ref(false)
const showShiftHandoverModal = ref(false)
const showEntrySettingsModal = ref(false)
const showMonitorSettingsModal = ref(false)
const showChargeDetailsModal = ref(false)
const showPlateModificationModal = ref(false)
const showVehicleEntryModal = ref(false)
const showPasswordChangeModal = ref(false)
const currentTime = ref('')

// 停车区域数据
const parkingAreas = ref([
  {
    id: 1,
    name: '私家车区域',
    totalSpaces: 300,
    remainingSpaces: 250
  },
  {
    id: 2,
    name: '电单车区域',
    totalSpaces: 300,
    remainingSpaces: 127
  },
  {
    id: 3,
    name: '地库',
    totalSpaces: 100,
    remainingSpaces: 94
  }
])

// 交班数据
const shiftData = ref({
  currentUser: 'aofe',
  lastShiftTime: '2025-07-25 18:53:53',
  exitData: {
    receivableAmount: '173MOP',
    freeAmount: '0MOP',
    discountTime: 0,
    actualDiscount: '0MOP',
    totalRelease: '5次',
    abnormalRelease: '3次',
    cashAmount: '173MOP',
    discountAmount: '0MOP',
    discountCount: 0,
    normalRelease: '2次',
    freeRelease: '0次'
  },
  centralData: {
    receivableAmount: '6MOP',
    freeAmount: '0MOP',
    discountTime: 0,
    cashAmount: '6MOP',
    discountAmount: '0MOP',
    discountCount: 0
  }
})

// 进出口设置数据
const entrySettingsData = ref({
  activeTab: '全部',
  searchKeyword: '',
  channels: [
    {
      id: 1,
      name: '私家车入口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 2,
      name: '私家车出口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 3,
      name: '电单车入口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 4,
      name: '电单车出口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 5,
      name: 'B2入口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 6,
      name: 'B2出口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 7,
      name: 'B2电单车入口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 8,
      name: 'B2电单车出口',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    },
    {
      id: 9,
      name: '私家车入口2',
      status: '已停到位',
      modalControl: '常规',
      distanceControl: '禁用'
    }
  ]
})

// 监控设置数据
const monitorSettingsData = ref({
  selectedMode: '无监控模式',
  independentWindow: false,
  modes: [
    { value: '无监控模式', label: '无监控模式' },
    { value: '2屏', label: '2屏' },
    { value: '4屏', label: '4屏' },
    { value: '6屏', label: '6屏' },
    { value: '9屏', label: '9屏' },
    { value: '12屏', label: '12屏' }
  ]
})

// 收费明细数据
const chargeDetailsData = ref({
  dateRange: {
    startDate: '2025-06-25',
    endDate: '2025-06-25',
    startTime: '19:54',
    endTime: '19:54'
  },
  records: [
    {
      id: 1,
      plateNumber: 'AC-228',
      receivableAmount: '3MOP',
      actualAmount: '3MOP',
      discountAmount: '0MOP',
      discountType: '0MOP',
      entryTime: '2025-07-25 10:32:00',
      exitTime: '2025-07-25 10:32:00',
      paymentMethod: '现金支付',
      operator: '操作员1',
      status: '已完成'
    },
    {
      id: 2,
      plateNumber: '2HNPT',
      receivableAmount: '1.5MOP',
      actualAmount: '1.5MOP',
      discountAmount: '0MOP',
      discountType: '0MOP',
      entryTime: '2025-07-25 17:46:12',
      exitTime: '2025-07-25 17:46:12',
      paymentMethod: '现金支付',
      operator: '操作员1',
      status: '已完成'
    },
    {
      id: 3,
      plateNumber: '',
      receivableAmount: '3MOP',
      actualAmount: '3MOP',
      discountAmount: '0MOP',
      discountType: '0MOP',
      entryTime: '2025-07-25 17:46:02',
      exitTime: '2025-07-25 17:46:02',
      paymentMethod: '现金支付',
      operator: '操作员1',
      status: '已完成'
    },
    {
      id: 4,
      plateNumber: 'T7318',
      receivableAmount: '3MOP',
      actualAmount: '3MOP',
      discountAmount: '0MOP',
      discountType: '0MOP',
      entryTime: '2025-07-25 14:16:04',
      exitTime: '2025-07-25 14:16:04',
      paymentMethod: '现金支付',
      operator: '操作员1',
      status: '已完成'
    },
    {
      id: 5,
      plateNumber: 'LG-1MOP',
      receivableAmount: 'LG-1MOP',
      actualAmount: '0MOP',
      discountAmount: '0MOP',
      discountType: '2025-04-16 6:06:42',
      entryTime: '2025-04-16 17:17:12',
      exitTime: '2025-04-16 17:17:12',
      paymentMethod: '现金支付',
      operator: '操作员1',
      status: '已完成'
    }
  ],
  statistics: {
    totalRecords: 17,
    totalReceivable: '17MOP',
    totalActual: '17MOP',
    totalDiscount: '0MOP'
  }
})

// 车牌修改数据
const plateModificationData = ref({
  searchFilters: {
    plateNumber: '',
    timeRange: '今天',
    status: '全部状态'
  },
  timeRangeOptions: ['今天', '昨天', '本周', '本月', '自定义'],
  statusOptions: ['全部状态', '待确认', '已确认', '已修改'],
  monitoringChannels: [
    {
      id: 1,
      name: '监控通道1',
      image: '/api/placeholder/300/200',
      plateNumber: 'ABC-07-24 17:30:16',
      timestamp: '2025-07-24 17:30:16',
      status: '待确认',
      confidence: 85
    },
    {
      id: 2,
      name: '监控通道2',
      image: '/api/placeholder/300/200',
      plateNumber: 'DEF-07-24 17:31:02',
      timestamp: '2025-07-24 17:31:02',
      status: '待确认',
      confidence: 92
    },
    {
      id: 3,
      name: '监控通道3',
      image: '/api/placeholder/300/200',
      plateNumber: 'GHI-07-24 17:29:47',
      timestamp: '2025-07-24 17:29:47',
      status: '待确认',
      confidence: 78
    }
  ]
})

// 车辆进入明细数据
const vehicleEntryData = ref({
  searchFilters: {
    startDate: '2025-07-25',
    endDate: '2025-07-25',
    startTime: '00:00:00',
    endTime: '23:59:59',
    plateNumber: '',
    entryMethod: '全部进入方式',
    vehicleType: '全部车辆类型',
    paymentStatus: '全部付款状态',
    entryChannel: '全部通道'
  },
  entryMethodOptions: ['全部进入方式', '刷卡进入', '扫码进入', '人工放行', '免费进入'],
  vehicleTypeOptions: ['全部车辆类型', '小型车', '大型车', '摩托车', '其他'],
  paymentStatusOptions: ['全部付款状态', '未付款', '已付款', '免费'],
  entryChannelOptions: ['全部通道', '通道1', '通道2', '通道3', '通道4'],
  records: [
    {
      id: 1,
      plateNumber: 'M-234',
      entryTime: '2025-07-25 14:14:14',
      entryMethod: '刷卡进入',
      vehicleType: '小型车',
      paymentStatus: '未付款',
      entryChannel: '通道1',
      operator: '停车场管理员001',
      remarks: 'edit',
      actions: '车辆进入'
    },
    {
      id: 2,
      plateNumber: 'G-123456',
      entryTime: '2025-07-25 17:32:24',
      entryMethod: '扫码进入',
      vehicleType: '小型车',
      paymentStatus: '未付款',
      entryChannel: '通道2',
      operator: '系统自动',
      remarks: 'edit',
      actions: '车辆进入'
    },
    {
      id: 3,
      plateNumber: '粤B12345',
      entryTime: '2025-07-25 10:15:14',
      entryMethod: '人工放行',
      vehicleType: '大型车',
      paymentStatus: '免费',
      entryChannel: '通道3',
      operator: '停车场管理员002',
      remarks: 'edit',
      actions: '车辆进入'
    }
  ]
})

// 密码修改数据
const passwordChangeData = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
  showCurrentPassword: false,
  showNewPassword: false,
  showConfirmPassword: false,
  passwordStrength: 0,
  passwordRequirements: {
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false
  }
})

// 移除未使用的变量以优化性能

// 方法 - 优化时间更新性能
let timeCache = ''
let lastSecond = -1

const updateTime = () => {
  const now = new Date()
  const currentSecond = now.getSeconds()

  // 只有秒数变化时才重新格式化，避免不必要的计算
  if (currentSecond !== lastSecond) {
    timeCache = now.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    lastSecond = currentSecond
    currentTime.value = timeCache
  }
}

const toggleMenu = () => {
  showMenu.value = !showMenu.value
  if (showMenu.value) {
    showUserMenu.value = false
  }
}

const toggleUserMenu = (event?: Event) => {
  if (showUserMenu.value) {
    // 如果要关闭菜单，直接关闭
    showUserMenu.value = false
    return
  }

  // 打开菜单时，先设置位置再显示
  showMenu.value = false

  const target = event?.target as HTMLElement
  const userContainer = target?.closest('.user-container') as HTMLElement
  const dropdown = userContainer?.querySelector('.user-dropdown') as HTMLElement

  if (userContainer && dropdown) {
    const rect = userContainer.getBoundingClientRect()

    // 立即设置位置，避免从底部出现
    dropdown.style.top = `${rect.bottom + 8}px`
    dropdown.style.right = `${window.innerWidth - rect.right}px`
    dropdown.style.left = 'auto'
    dropdown.style.position = 'fixed'

    // 然后显示菜单
    showUserMenu.value = true
  }
}

const toggleChannelList = () => {
  showChannelList.value = !showChannelList.value
  emit('toggleChannelList')
}

const navigateTo = (path: string) => {
  router.push(path)
  showMenu.value = false
}

const handleMenuClick = (item: any) => {
  showMenu.value = false
  if (item.action) {
    // 触发自定义事件
    emit(item.action)
  } else {
    navigateTo(item.path)
  }
}

const refreshData = () => {
  emit('refresh')
}

const goBack = () => {
  emit('goBack')
}

// 移除未使用的方法以优化性能

// logout方法已合并到handleLogout中

// 新增的功能方法
const viewMonitorSettings = () => {
  showUserMenu.value = false
  showMonitorSettingsModal.value = true
}

const closeMonitorSettingsModal = () => {
  showMonitorSettingsModal.value = false
}

const selectMonitorMode = (mode: string) => {
  monitorSettingsData.value.selectedMode = mode
}

const toggleIndependentWindow = () => {
  monitorSettingsData.value.independentWindow = !monitorSettingsData.value.independentWindow
}

const cancelMonitorSettings = () => {
  // 重置为默认设置
  monitorSettingsData.value.selectedMode = '无监控模式'
  monitorSettingsData.value.independentWindow = false
  closeMonitorSettingsModal()
  notification.info('已取消监控设置', '系统提示')
}

const confirmMonitorSettings = () => {
  // 应用监控设置
  closeMonitorSettingsModal()

  let message = `监控模式已设置为: ${monitorSettingsData.value.selectedMode}`
  if (monitorSettingsData.value.independentWindow) {
    message += '，独立窗口模式已启用'
  }

  notification.success(message, '设置成功')
  console.log('监控设置已应用:', {
    mode: monitorSettingsData.value.selectedMode,
    independentWindow: monitorSettingsData.value.independentWindow
  })
}

const viewEntrySettings = () => {
  showUserMenu.value = false
  showEntrySettingsModal.value = true
}

const closeEntrySettingsModal = () => {
  showEntrySettingsModal.value = false
}

const setActiveTab = (tab: string) => {
  entrySettingsData.value.activeTab = tab
}

const searchChannels = () => {
  // 搜索通道功能
  console.log('搜索通道:', entrySettingsData.value.searchKeyword)
}

const toggleModalControl = (channel: any) => {
  channel.modalControl = channel.modalControl === '常规' ? '禁用' : '常规'
  notification.success(`${channel.name} 模式控制已切换为: ${channel.modalControl}`, '设置成功')
}

const toggleDistanceControl = (channel: any) => {
  channel.distanceControl = channel.distanceControl === '禁用' ? '启用' : '禁用'
  notification.success(`${channel.name} 距离控制已切换为: ${channel.distanceControl}`, '设置成功')
}

const getFilteredChannels = () => {
  let filtered = entrySettingsData.value.channels

  // 根据标签页过滤
  if (entrySettingsData.value.activeTab === '入口地点') {
    filtered = filtered.filter(channel => channel.name.includes('入口'))
  } else if (entrySettingsData.value.activeTab === '出口地点') {
    filtered = filtered.filter(channel => channel.name.includes('出口'))
  }

  // 根据搜索关键词过滤
  if (entrySettingsData.value.searchKeyword) {
    filtered = filtered.filter(channel =>
      channel.name.toLowerCase().includes(entrySettingsData.value.searchKeyword.toLowerCase())
    )
  }

  return filtered
}

const viewChargeDetails = () => {
  showUserMenu.value = false
  showChargeDetailsModal.value = true
}

const closeChargeDetailsModal = () => {
  showChargeDetailsModal.value = false
}

const searchChargeDetails = () => {
  // 根据时间范围搜索收费明细
  console.log('搜索收费明细:', chargeDetailsData.value.dateRange)
  notification.success('收费明细已更新', '搜索成功')
}

const exportChargeDetails = () => {
  // 导出收费明细
  console.log('导出收费明细')
  notification.success('收费明细导出成功', '导出完成')
}

const formatDateTime = (dateStr: string) => {
  // 格式化日期时间显示
  if (!dateStr) return '--'
  return dateStr.replace('T', ' ')
}

const getStatusColor = (status: string) => {
  // 根据状态返回颜色
  switch (status) {
    case '已完成':
      return 'success'
    case '进行中':
      return 'warning'
    case '已取消':
      return 'danger'
    default:
      return 'info'
  }
}

const handleLogout = async () => {
  showUserMenu.value = false

  const confirmed = await notification.confirm({
    title: '确认注销',
    message: '您确定要注销当前账户吗？注销后需要重新登录才能继续使用系统',
    type: 'warning',
    confirmText: '确认注销',
    cancelText: '取消'
  })

  if (confirmed) {
    await performLogout()
  }
}

const cancelLogout = () => {
  showLogoutModal.value = false
}

const performLogout = async () => {
  try {
    await authStore.logout()
    notification.success('注销成功', '系统提示')
    router.push('/login')
  } catch (error) {
    console.error('注销失败:', error)
    notification.error('注销失败，请重试', '系统错误')
  }
}

const confirmLogout = async () => {
  showLogoutModal.value = false
  await performLogout()
}

// 停车位弹窗相关方法
const openParkingModal = () => {
  showParkingModal.value = true
}

const closeParkingModal = () => {
  showParkingModal.value = false
}

const modifyAreaSpaces = (area: any) => {
  // 验证输入值
  if (area.remainingSpaces < 0) {
    area.remainingSpaces = 0
    notification.warning('剩余车位数不能小于0', '输入错误')
    return
  }

  if (area.remainingSpaces > area.totalSpaces) {
    area.remainingSpaces = area.totalSpaces
    notification.warning('剩余车位数不能大于总车位数', '输入错误')
    return
  }

  // 这里可以调用API保存数据
  console.log(`修改${area.name}剩余车位为: ${area.remainingSpaces}`)
  notification.success(`${area.name}剩余车位已修改为: ${area.remainingSpaces}`, '修改成功')
}

const viewVehicleEntry = () => {
  showUserMenu.value = false
  showVehicleEntryModal.value = true
}

const closeVehicleEntryModal = () => {
  showVehicleEntryModal.value = false
}

const searchVehicleEntry = () => {
  // 根据筛选条件搜索车辆进入数据
  console.log('搜索车辆进入明细:', vehicleEntryData.value.searchFilters)
  notification.success('车辆进入数据已更新', '搜索成功')
}

const exportVehicleEntry = () => {
  // 导出车辆进入数据
  console.log('导出车辆进入数据')
  notification.success('车辆进入数据导出成功', '导出完成')
}

const editVehicleEntry = (recordId: number) => {
  // 编辑车辆进入记录
  console.log('编辑车辆进入记录:', recordId)
  notification.info('编辑功能开发中', '提示')
}

const viewPasswordChange = () => {
  showUserMenu.value = false
  showPasswordChangeModal.value = true
  // 重置密码修改表单
  passwordChangeData.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    showCurrentPassword: false,
    showNewPassword: false,
    showConfirmPassword: false,
    passwordStrength: 0,
    passwordRequirements: {
      minLength: false,
      hasUppercase: false,
      hasLowercase: false,
      hasNumber: false,
      hasSpecialChar: false
    }
  }
}

const closePasswordChangeModal = () => {
  showPasswordChangeModal.value = false
}

const togglePasswordVisibility = (field: string) => {
  // 切换密码显示/隐藏
  switch (field) {
    case 'current':
      passwordChangeData.value.showCurrentPassword = !passwordChangeData.value.showCurrentPassword
      break
    case 'new':
      passwordChangeData.value.showNewPassword = !passwordChangeData.value.showNewPassword
      break
    case 'confirm':
      passwordChangeData.value.showConfirmPassword = !passwordChangeData.value.showConfirmPassword
      break
  }
}

const checkPasswordStrength = (password: string) => {
  // 检查密码强度
  const requirements = passwordChangeData.value.passwordRequirements

  requirements.minLength = password.length >= 8
  requirements.hasUppercase = /[A-Z]/.test(password)
  requirements.hasLowercase = /[a-z]/.test(password)
  requirements.hasNumber = /\d/.test(password)
  requirements.hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  const score = Object.values(requirements).filter(Boolean).length
  passwordChangeData.value.passwordStrength = score

  return score
}

const validatePasswords = () => {
  // 验证密码
  const { currentPassword, newPassword, confirmPassword } = passwordChangeData.value

  if (!currentPassword) {
    notification.error('请输入当前密码', '验证失败')
    return false
  }

  if (!newPassword) {
    notification.error('请输入新密码', '验证失败')
    return false
  }

  if (newPassword.length < 8) {
    notification.error('新密码长度至少8位', '验证失败')
    return false
  }

  if (newPassword !== confirmPassword) {
    notification.error('两次输入的新密码不一致', '验证失败')
    return false
  }

  if (currentPassword === newPassword) {
    notification.error('新密码不能与当前密码相同', '验证失败')
    return false
  }

  const strength = checkPasswordStrength(newPassword)
  if (strength < 3) {
    notification.warning('密码强度较弱，建议包含大小写字母、数字和特殊字符', '安全提示')
  }

  return true
}

const confirmPasswordChange = async () => {
  // 确认密码修改
  if (!validatePasswords()) {
    return
  }

  try {
    // 调用API修改密码
    const success = await authStore.changePassword(
      passwordChangeData.value.currentPassword,
      passwordChangeData.value.newPassword
    )

    if (success) {
      closePasswordChangeModal()
      notification.success('密码修改成功，请重新登录', '修改成功')

      // 延迟2秒后自动退出登录
      setTimeout(() => {
        authStore.logout()
        router.push('/login')
      }, 2000)
    } else {
      notification.error('密码修改失败，请检查当前密码是否正确', '修改失败')
    }
  } catch (error: any) {
    console.error('修改密码失败:', error)
    const errorMsg = error.message || '密码修改失败，请稍后重试'
    notification.error(errorMsg, '修改失败')
  }
}

const viewShiftHandover = () => {
  showUserMenu.value = false
  showShiftHandoverModal.value = true
}

const closeShiftHandoverModal = () => {
  showShiftHandoverModal.value = false
}

const takeShift = () => {
  // 取消交班
  closeShiftHandoverModal()
  notification.info('已取消交班', '系统提示')
}

const confirmShift = async () => {
  // 确认交班
  const confirmed = await notification.confirm({
    title: '确认交班',
    message: '确认要进行交班操作吗？交班后当前班次数据将被保存。',
    type: 'warning',
    confirmText: '确认交班',
    cancelText: '取消'
  })

  if (confirmed) {
    closeShiftHandoverModal()
    notification.success('交班成功', '系统提示')
    // 这里可以调用交班API
    console.log('执行交班操作')
  }
}

const viewPlateModification = () => {
  showUserMenu.value = false
  showPlateModificationModal.value = true
}

const closePlateModificationModal = () => {
  showPlateModificationModal.value = false
}

const searchPlateModification = () => {
  // 根据筛选条件搜索车牌数据
  console.log('搜索车牌修改:', plateModificationData.value.searchFilters)
  notification.success('车牌数据已更新', '搜索成功')
}

const confirmPlateModification = (channelId: number, newPlateNumber: string) => {
  // 确认车牌修改
  const channel = plateModificationData.value.monitoringChannels.find(c => c.id === channelId)
  if (channel) {
    channel.plateNumber = newPlateNumber
    channel.status = '已确认'
    notification.success(`车牌已修改为: ${newPlateNumber}`, '修改成功')
  }
}

const exportPlateData = () => {
  // 导出车牌数据
  console.log('导出车牌数据')
  notification.success('车牌数据导出成功', '导出完成')
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.menu-container')) {
    showMenu.value = false
  }
  if (!target.closest('.user-container')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  // 初始化时间
  updateTime()
  const timeInterval = setInterval(updateTime, 1000)

  // 添加点击外部事件监听
  document.addEventListener('click', handleClickOutside)

  // 清理函数
  onUnmounted(() => {
    clearInterval(timeInterval)
    document.removeEventListener('click', handleClickOutside)
  })
})
</script>

<style scoped>
.unified-top-bar {
  height: 80px;
  background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  color: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.unified-top-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 左侧区域 */
.top-left-section {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  z-index: 1;
}

.menu-container {
  position: relative;
}

.menu-button {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: #ffffff;
  padding: 10px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.menu-button:hover,
.menu-button.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.menu-icon {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.menu-button:hover .menu-icon {
  transform: scale(1.1);
}

.menu-text {
  font-weight: 600;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
}

.dropdown-arrow.active {
  transform: rotate(180deg);
}

/* Logo区域 */
.logo-section {
  display: flex;
  align-items: center;
  gap: 32px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.logo-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-brand {
  font-size: 24px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.logo-version {
  font-size: 12px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-shadow: none;
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.page-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.page-icon {
  font-size: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.page-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 中央Logo区域 */
.top-center-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 0 0 auto;
  z-index: 1;
}

.logo-container-center {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 20px;
  /* 去掉透明底 */
  border-radius: 16px;
}

/* 右侧时间显示 */
.time-display-right {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-right: 16px;
}

.current-time-only {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

/* 右侧区域 */
.top-right-section {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
  z-index: 1;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.status-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.status-value {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn.refresh {
  border-color: rgba(76, 175, 80, 0.5);
}

.action-btn.refresh:hover:not(:disabled) {
  background: rgba(76, 175, 80, 0.2);
}

.action-btn.back {
  border-color: rgba(255, 193, 7, 0.5);
}

.action-btn.back:hover {
  background: rgba(255, 193, 7, 0.2);
}

.btn-icon {
  font-size: 14px;
}

.btn-text {
  font-weight: 600;
}

/* 用户信息区域 */
.user-container {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: none;
}

.user-info:hover,
.user-info.active {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.avatar-icon {
  font-size: 18px;
  color: white;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-role {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* 下拉菜单样式 */
.menu-dropdown,
.user-dropdown {
  position: fixed;
  top: 0;
  right: 0;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
  min-width: 220px;
  z-index: 100000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-dropdown {
  left: 0;
  min-width: 240px;
}

.user-dropdown {
  right: 0;
  min-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.menu-dropdown.show,
.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.menu-item,
.user-dropdown-item {
  padding: 14px 18px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.menu-item::before,
.user-dropdown-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  z-index: 0;
}

.menu-item:hover::before,
.user-dropdown-item:hover::before {
  width: 4px;
}

.menu-item:hover,
.user-dropdown-item:hover {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  color: #1e3c72;
  transform: translateX(4px);
}

.menu-item:first-child,
.user-dropdown-item:first-child {
  border-radius: 12px 12px 0 0;
}

.menu-item:last-child,
.user-dropdown-item:last-child {
  border-radius: 0 0 12px 12px;
  border-bottom: none;
}

.menu-item-icon,
.dropdown-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  z-index: 1;
  position: relative;
}

.user-dropdown-item.logout {
  color: #dc3545;
  border-top: 1px solid rgba(220, 53, 69, 0.1);
}

.user-dropdown-item.logout::before {
  background: linear-gradient(90deg, #dc3545, #c82333);
}

.user-dropdown-item.logout:hover {
  background: linear-gradient(90deg, rgba(220, 53, 69, 0.05), rgba(200, 35, 51, 0.05));
  color: #721c24;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .unified-top-bar {
    padding: 0 16px;
  }

  .logo-container {
    padding: 6px 16px;
  }

  .logo-brand {
    font-size: 20px;
  }

  .page-title {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .unified-top-bar {
    height: 70px;
    padding: 0 12px;
  }

  .top-left-section {
    gap: 16px;
  }

  .logo-container {
    padding: 4px 12px;
  }

  .logo-brand {
    font-size: 18px;
  }

  .logo-subtitle {
    display: none;
  }

  .current-time {
    font-size: 20px;
  }

  .time-display {
    padding: 8px 16px;
  }

  .status-item {
    display: none;
  }

  .action-buttons {
    gap: 4px;
  }

  .action-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .btn-text {
    display: none;
  }
}

/* 用户下拉菜单新样式 */
.user-dropdown-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  margin: -1px -1px 0 -1px;
}

.user-avatar-large {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
}

.avatar-text {
  color: white;
}

.dropdown-arrow-user {
  font-size: 12px;
  opacity: 0.8;
}

.offline-data-section {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-row {
  display: flex;
  gap: 12px;
}

.data-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  min-height: 36px;
}

.data-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.data-value {
  font-size: 12px;
  color: #333;
  font-weight: 600;
}

.function-buttons {
  padding: 20px;
  background: white;
}

.button-row {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.button-row:last-child {
  margin-bottom: 0;
}

.function-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  min-height: 60px;
  background: #ffffff;
  color: #495057;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.function-btn.primary {
  border-color: #4a90e2;
  color: #4a90e2;
}

.function-btn.secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.function-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #4a90e2;
  color: #4a90e2;
}

.function-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.function-btn .btn-icon {
  font-size: 18px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.function-btn:hover .btn-icon {
  opacity: 1;
}

.function-btn span:last-child {
  font-size: 11px;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* 美化的注销确认弹窗样式 */
.logout-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

.logout-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  min-width: 400px;
  max-width: 500px;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logout-modal-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 24px;
  text-align: center;
  position: relative;
}

.logout-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.logout-icon-symbol {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logout-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logout-modal-body {
  padding: 32px 24px;
  text-align: center;
}

.logout-message {
  font-size: 16px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.logout-submessage {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.logout-modal-footer {
  padding: 20px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
  background: #f8f9fa;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.logout-btn.cancel {
  background: #6c757d;
  color: white;
}

.logout-btn.cancel:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.logout-btn.confirm {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

.logout-btn.confirm:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.logout-btn .btn-icon {
  font-size: 16px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 停车位弹窗样式 */
.parking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

.parking-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  min-width: 600px;
  max-width: 800px;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.parking-modal-header {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.parking-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.parking-modal-body {
  padding: 24px;
}

.parking-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header-cell {
  padding: 16px 12px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #e9ecef;
}

.header-cell:last-child {
  border-right: none;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr;
  border-bottom: 1px solid #e9ecef;
  transition: background 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 16px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #e9ecef;
}

.table-cell:last-child {
  border-right: none;
}

.area-name {
  font-weight: 500;
  color: #333;
}

.total-spaces {
  font-weight: 600;
  color: #28a745;
}

.remaining-spaces {
  padding: 8px 12px;
}

.spaces-input {
  width: 80px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  transition: border-color 0.3s ease;
}

.spaces-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.modify-btn {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modify-btn:hover {
  background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* 让剩余车位可点击 */
.status-item {
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transform: translateY(-1px);
}

/* 特殊按钮样式 - 重要功能 */
.function-btn.important {
  border-color: #28a745;
  color: #28a745;
}

.function-btn.important:hover {
  border-color: #28a745;
  color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

/* 警告类按钮样式 */
.function-btn.warning {
  border-color: #ffc107;
  color: #856404;
}

.function-btn.warning:hover {
  border-color: #ffc107;
  color: #856404;
  background: rgba(255, 193, 7, 0.05);
}

/* 交班确认弹窗样式 */
.shift-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.shift-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 800px;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.shift-modal-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shift-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.shift-modal-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.shift-info {
  margin-bottom: 24px;
}

.shift-user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.user-label {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.shift-time {
  font-size: 13px;
  color: #6c757d;
}

.shift-section {
  margin-bottom: 24px;
}

.section-header {
  background: #e9ecef;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  border-radius: 6px 6px 0 0;
  border: 1px solid #dee2e6;
  text-align: center;
}

.data-grid {
  border: 1px solid #dee2e6;
  border-top: none;
  border-radius: 0 0 6px 6px;
  overflow: hidden;
}

.data-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.data-row:last-child {
  border-bottom: none;
}

.data-item {
  flex: 1;
  display: flex;
  border-right: 1px solid #dee2e6;
}

.data-item:last-child {
  border-right: none;
}

.data-label {
  flex: 1;
  padding: 12px 16px;
  background: #f8f9fa;
  font-size: 13px;
  color: #495057;
  border-right: 1px solid #dee2e6;
  text-align: center;
}

.data-value {
  flex: 1;
  padding: 12px 16px;
  background: #ffffff;
  font-size: 13px;
  color: #2c3e50;
  font-weight: 600;
  text-align: center;
}

.shift-modal-footer {
  padding: 20px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.shift-btn {
  padding: 10px 24px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.shift-btn.cancel {
  background: #ffffff;
  border-color: #6c757d;
  color: #6c757d;
}

.shift-btn.cancel:hover {
  background: #6c757d;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.shift-btn.confirm {
  background: #ffffff;
  border-color: #007bff;
  color: #007bff;
}

.shift-btn.confirm:hover {
  background: #007bff;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shift-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .shift-modal-header {
    padding: 16px 20px;
  }

  .shift-modal-body {
    padding: 20px;
  }

  .shift-modal-footer {
    padding: 16px 20px;
    flex-direction: column;
  }

  .shift-btn {
    width: 100%;
  }

  .data-label,
  .data-value {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* 进出口设置弹窗样式 */
.entry-settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.entry-settings-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 900px;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.entry-settings-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entry-settings-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.entry-settings-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.entry-settings-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 20px;
}

.tab-buttons {
  display: flex;
  gap: 0;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  overflow: hidden;
}

.tab-btn {
  padding: 10px 20px;
  background: #f8f9fa;
  border: none;
  border-right: 1px solid #dee2e6;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn:last-child {
  border-right: none;
}

.tab-btn.active {
  background: #007bff;
  color: #ffffff;
}

.tab-btn:hover:not(.active) {
  background: #e9ecef;
  color: #495057;
}

.search-and-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  position: relative;
}

.search-input {
  padding: 10px 16px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  width: 200px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 10px 16px;
  border: 1px solid #007bff;
  border-radius: 6px;
  background: #ffffff;
  color: #007bff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #007bff;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.channels-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.header-cell {
  padding: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.channel-name {
  flex: 2;
}

.header-cell.channel-status {
  flex: 1.5;
}

.header-cell.modal-control,
.header-cell.distance-control {
  flex: 2;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 16px;
  font-size: 14px;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.channel-name {
  flex: 2;
  text-align: left;
  justify-content: flex-start;
  font-weight: 500;
}

.table-cell.channel-status {
  flex: 1.5;
}

.table-cell.modal-control,
.table-cell.distance-control {
  flex: 2;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.control-buttons {
  display: flex;
  gap: 4px;
}

.control-btn {
  padding: 6px 16px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.control-btn.active {
  background: #007bff;
  color: #ffffff;
  border-color: #007bff;
}

.control-btn:hover:not(.active) {
  background: #e9ecef;
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .entry-settings-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .entry-settings-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .search-and-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .action-buttons {
    justify-content: center;
  }

  .table-header,
  .table-row {
    font-size: 12px;
  }

  .header-cell,
  .table-cell {
    padding: 12px 8px;
  }

  .control-btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 45px;
  }
}

/* 监控设置弹窗样式 */
.monitor-settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.monitor-settings-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 800px;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.monitor-settings-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-settings-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.monitor-settings-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.monitor-notice {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #856404;
  line-height: 1.5;
}

.monitor-modes {
  display: flex;
  align-items: center;
  gap: 0;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.mode-btn {
  padding: 12px 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-right: none;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.mode-btn:first-child {
  border-radius: 6px 0 0 6px;
}

.mode-btn:nth-child(6) {
  border-right: 1px solid #dee2e6;
  border-radius: 0 6px 6px 0;
}

.mode-btn.active {
  background: #007bff;
  color: #ffffff;
  border-color: #007bff;
}

.mode-btn:hover:not(.active) {
  background: #e9ecef;
  color: #495057;
}

.independent-window-option {
  margin-left: 24px;
  display: flex;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkbox-input:checked {
  background: #007bff;
  border-color: #007bff;
}

.checkbox-label {
  font-weight: 500;
  user-select: none;
}

.monitor-display-area {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.monitor-placeholder {
  text-align: center;
  color: #6c757d;
}

.placeholder-text {
  display: block;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.placeholder-mode {
  display: block;
  font-size: 14px;
  color: #007bff;
  font-weight: 600;
}

.monitor-settings-footer {
  padding: 20px 0 0 0;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.monitor-btn {
  padding: 10px 32px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.monitor-btn.cancel {
  background: #ffffff;
  border-color: #6c757d;
  color: #6c757d;
}

.monitor-btn.cancel:hover {
  background: #6c757d;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.monitor-btn.confirm {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.monitor-btn.confirm:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-settings-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .monitor-settings-header {
    padding: 16px 20px;
  }

  .monitor-settings-body {
    padding: 20px;
  }

  .monitor-modes {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .mode-btn {
    border-radius: 6px !important;
    border-right: 1px solid #dee2e6 !important;
    margin-bottom: 8px;
  }

  .independent-window-option {
    margin-left: 0;
    justify-content: center;
  }

  .monitor-display-area {
    min-height: 200px;
  }

  .monitor-settings-footer {
    flex-direction: column;
  }

  .monitor-btn {
    width: 100%;
  }
}

/* 收费明细弹窗样式 */
.charge-details-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.charge-details-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 1200px;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.charge-details-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.charge-details-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.charge-details-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.date-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-group label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  min-width: 70px;
}

.date-input,
.time-input {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.date-input {
  width: 140px;
}

.time-input {
  width: 100px;
}

.date-input:focus,
.time-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.date-separator {
  font-size: 16px;
  color: #6c757d;
  font-weight: 500;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.filter-btn {
  padding: 8px 20px;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: #ffffff;
  color: #007bff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: #007bff;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.charge-details-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.header-cell {
  padding: 12px 8px;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  flex-shrink: 0;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.plate-number {
  width: 100px;
}

.header-cell.amount {
  width: 80px;
}

.header-cell.discount {
  width: 80px;
}

.header-cell.datetime {
  width: 140px;
}

.header-cell.payment {
  width: 80px;
}

.header-cell.operator {
  width: 80px;
}

.header-cell.status {
  width: 80px;
}

.header-cell.action {
  width: 60px;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 8px;
  font-size: 12px;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.plate-number {
  width: 100px;
  font-weight: 500;
}

.table-cell.amount {
  width: 80px;
  font-weight: 600;
  color: #28a745;
}

.table-cell.discount {
  width: 80px;
}

.table-cell.datetime {
  width: 140px;
  font-size: 11px;
}

.table-cell.payment {
  width: 80px;
}

.table-cell.operator {
  width: 80px;
}

.table-cell.status {
  width: 80px;
}

.table-cell.action {
  width: 60px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.danger {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-badge.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.action-link {
  background: none;
  border: none;
  color: #007bff;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.action-link:hover {
  color: #0056b3;
}

.charge-statistics {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.stats-item {
  display: flex;
  align-items: center;
}

.stats-label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charge-details-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .charge-details-header {
    padding: 16px 20px;
  }

  .charge-details-body {
    padding: 20px;
  }

  .date-filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .date-inputs {
    flex-direction: column;
    gap: 12px;
  }

  .filter-actions {
    justify-content: center;
  }

  .charge-details-table {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    min-width: 800px;
  }

  .charge-statistics {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .stats-item {
    justify-content: center;
  }
}

/* 车牌修改弹窗样式 */
.plate-modification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.plate-modification-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 1200px;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.plate-modification-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plate-modification-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.plate-modification-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.search-filter-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  min-width: 80px;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  min-width: 120px;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.monitoring-channel {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
  transition: box-shadow 0.3s ease;
}

.monitoring-channel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.channel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-name {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.channel-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.channel-status.待确认 {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.channel-status.已确认 {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.channel-status.已修改 {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.channel-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.channel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #f8f9fa;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
}

.confidence-badge {
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.channel-info {
  padding: 16px;
}

.plate-info {
  margin-bottom: 12px;
}

.plate-info label {
  display: block;
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  margin-bottom: 6px;
}

.plate-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.plate-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.timestamp-info {
  margin-bottom: 12px;
}

.timestamp {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.channel-actions {
  display: flex;
  justify-content: center;
}

.action-btn {
  padding: 8px 20px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.confirm {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.action-btn.confirm:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plate-modification-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .plate-modification-header {
    padding: 16px 20px;
  }

  .plate-modification-body {
    padding: 20px;
  }

  .search-filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group label {
    min-width: auto;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }

  .monitoring-grid {
    grid-template-columns: 1fr;
  }
}

/* 车辆进入明细弹窗样式 */
.vehicle-entry-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.vehicle-entry-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 1200px;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.vehicle-entry-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vehicle-entry-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.vehicle-entry-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.entry-filter-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  min-width: 80px;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.date-input {
  width: 140px;
}

.time-input {
  width: 100px;
}

.filter-separator {
  font-size: 16px;
  color: #6c757d;
  font-weight: 500;
  margin: 0 8px;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.action-checkboxes {
  display: flex;
  gap: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
}

.checkbox-input {
  width: 16px;
  height: 16px;
  border: 2px solid #dee2e6;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkbox-input:checked {
  background: #007bff;
  border-color: #007bff;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.vehicle-entry-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.header-cell {
  padding: 12px 8px;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  flex-shrink: 0;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.plate-number {
  width: 120px;
}

.header-cell.entry-time {
  width: 140px;
}

.header-cell.entry-method {
  width: 100px;
}

.header-cell.vehicle-type {
  width: 100px;
}

.header-cell.payment-status {
  width: 100px;
}

.header-cell.entry-channel {
  width: 100px;
}

.header-cell.operator {
  width: 140px;
}

.header-cell.remarks {
  width: 80px;
}

.header-cell.actions {
  width: 100px;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 8px;
  font-size: 12px;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.plate-number {
  width: 120px;
  font-weight: 500;
}

.table-cell.entry-time {
  width: 140px;
  font-size: 11px;
}

.table-cell.entry-method {
  width: 100px;
}

.table-cell.vehicle-type {
  width: 100px;
}

.table-cell.payment-status {
  width: 100px;
}

.table-cell.entry-channel {
  width: 100px;
}

.table-cell.operator {
  width: 140px;
  font-size: 11px;
}

.table-cell.remarks {
  width: 80px;
}

.table-cell.actions {
  width: 100px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.未付款 {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.已付款 {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.免费 {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.action-link {
  background: none;
  border: none;
  color: #007bff;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.action-link:hover {
  color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-entry-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .vehicle-entry-header {
    padding: 16px 20px;
  }

  .vehicle-entry-body {
    padding: 20px;
  }

  .entry-filter-section {
    padding: 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group label {
    min-width: auto;
  }

  .filter-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-checkboxes {
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
  }

  .vehicle-entry-table {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    min-width: 1000px;
  }
}

/* 密码修改弹窗样式 */
.password-change-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.password-change-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 500px;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.password-change-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.password-change-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.password-change-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.password-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 50px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.password-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.3s ease;
  padding: 4px;
}

.password-toggle:hover {
  color: #495057;
}

.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: #dc3545;
}

.strength-fill.medium {
  background: #ffc107;
}

.strength-fill.strong {
  background: #28a745;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
}

.password-match {
  margin-top: 8px;
  font-size: 12px;
  font-weight: 500;
}

.password-match.match {
  color: #28a745;
}

.password-match.mismatch {
  color: #dc3545;
}

.password-requirements {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.requirements-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.requirements-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.requirements-list li {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 6px;
  padding-left: 20px;
  position: relative;
  transition: color 0.3s ease;
}

.requirements-list li:last-child {
  margin-bottom: 0;
}

.requirements-list li:before {
  content: '○';
  position: absolute;
  left: 0;
  color: #dee2e6;
  transition: all 0.3s ease;
}

.requirements-list li.met {
  color: #28a745;
}

.requirements-list li.met:before {
  content: '✓';
  color: #28a745;
}

.password-change-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.password-btn {
  padding: 10px 32px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.password-btn.cancel {
  background: #ffffff;
  border-color: #6c757d;
  color: #6c757d;
}

.password-btn.cancel:hover {
  background: #6c757d;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.password-btn.confirm {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.password-btn.confirm:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.password-btn.confirm:disabled {
  background: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .password-change-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .password-change-header {
    padding: 16px 20px;
  }

  .password-change-body {
    padding: 20px;
  }

  .password-change-footer {
    flex-direction: column;
  }

  .password-btn {
    width: 100%;
  }
}
</style>
