{"format": 1, "restore": {"E:\\work\\ParkingfeePaymentSystem\\backend\\ParkingBoothApi.csproj": {}}, "projects": {"E:\\work\\ParkingfeePaymentSystem\\backend\\ParkingBoothApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\work\\ParkingfeePaymentSystem\\backend\\ParkingBoothApi.csproj", "projectName": "ParkingBoothApi", "projectPath": "E:\\work\\ParkingfeePaymentSystem\\backend\\ParkingBoothApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\work\\ParkingfeePaymentSystem\\backend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\program\\tools\\Microsoft\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/nexifyhk/_packaging/NexTore/nuget/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}