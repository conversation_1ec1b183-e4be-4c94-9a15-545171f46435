<template>
  <div class="parking-management">
    <!-- Unified Top Bar -->
    <TopMenuBar
      page-title="系统首页"
      page-icon="🏠"
      status-label="剩余车位"
      :status-value="`${availableParkingSpaces}个`"
      :is-refreshing="isRefreshing"
      @refresh="refreshData"
      @go-back="showSettings"
      @toggleChannelList="toggleChannelList"
    />

    <!-- Main Content Area -->
    <div class="main-content-area">
      <!-- 左侧浮动入口列表面板 -->
      <div class="left-floating-channel-panel" v-show="showChannelList">
        <div class="panel-header">
          <h3>入口列表</h3>
          <button class="close-btn" @click="showChannelList = false">×</button>
        </div>
        <div class="channel-list">
          <div class="channel-item" :class="{ active: selectedChannel === 'private-entrance' }" @click="selectChannel('private-entrance')">
            <span class="channel-icon">🚗</span>
            <span class="channel-text">私家车入口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'private-exit' }" @click="selectChannel('private-exit')">
            <span class="channel-icon">🚗</span>
            <span class="channel-text">私家车出口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'electric-entrance' }" @click="selectChannel('electric-entrance')">
            <span class="channel-icon">🛵</span>
            <span class="channel-text">电单车入口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'electric-exit' }" @click="selectChannel('electric-exit')">
            <span class="channel-icon">🛵</span>
            <span class="channel-text">电单车出口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'b2-entrance' }" @click="selectChannel('b2-entrance')">
            <span class="channel-icon">🚪</span>
            <span class="channel-text">B2入口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'b2-exit' }" @click="selectChannel('b2-exit')">
            <span class="channel-icon">🚪</span>
            <span class="channel-text">B2出口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'b2-electric-entrance' }" @click="selectChannel('b2-electric-entrance')">
            <span class="channel-icon">🛵</span>
            <span class="channel-text">B2电单车入口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'b2-electric-exit' }" @click="selectChannel('b2-electric-exit')">
            <span class="channel-icon">🛵</span>
            <span class="channel-text">B2电单车出口</span>
          </div>
          <div class="channel-item" :class="{ active: selectedChannel === 'private-entrance-2' }" @click="selectChannel('private-entrance-2')">
            <span class="channel-icon">🚗</span>
            <span class="channel-text">私家车入口2</span>
          </div>
        </div>
      </div>
      <!-- Center Content Area -->
      <div class="center-content-area">
        <!-- 上方双视频区域 -->
        <div class="video-top-section">
          <div class="video-window">
            <div class="video-header">
              <span class="video-title">入口监控</span>
              <span class="video-time">{{ currentTime }}</span>
            </div>
            <div class="video-content">
              <div class="video-placeholder">
                <div class="camera-icon">📹</div>
                <div class="camera-text">入口摄像头</div>
              </div>
            </div>
            <div class="video-footer">
              <span class="video-status">入口通道1 | 在线</span>
            </div>
          </div>

          <div class="video-window">
            <div class="video-header">
              <span class="video-title">出口监控</span>
              <span class="video-time">{{ currentTime }}</span>
            </div>
            <div class="video-content">
              <div class="video-placeholder">
                <div class="camera-icon">📹</div>
                <div class="camera-text">出口摄像头</div>
                <div class="license-plate">MY-12-34</div>
              </div>
            </div>
            <div class="video-footer">
              <span class="video-status">MY1234 | 识别中</span>
            </div>
          </div>
        </div>

        <!-- 下方三视频区域 -->
        <div class="video-bottom-section">
          <div class="video-controls">
            <span class="controls-title">出场监控记录</span>
            <div class="controls-buttons">
              <button class="control-btn">◀</button>
              <button class="control-btn">▶</button>
              <!-- Electron 测试按钮 -->
              <button class="electron-test-btn" @click="testElectronFeatures" v-if="isElectronApp">
                🖥️ 测试桌面功能
              </button>
            </div>
          </div>

          <div class="video-grid">
            <div class="video-window small">
              <div class="video-content">
                <div class="video-placeholder">
                  <div class="camera-icon">📹</div>
                </div>
              </div>
            </div>
            <div class="video-window small">
              <div class="video-content">
                <div class="video-placeholder">
                  <div class="camera-icon">📹</div>
                </div>
              </div>
            </div>
            <div class="video-window small">
              <div class="video-content">
                <div class="video-placeholder">
                  <div class="camera-icon">📹</div>
                </div>
              </div>
            </div>
          </div>

          <div class="bottom-status">
            <span class="status-text">出场不成功，请处理</span>
            <button class="handle-btn">手动处理</button>
          </div>
        </div>
      </div>

      <!-- Right Control Panel -->
      <div class="right-control-panel">
        <div class="panel-header">
          <h3>出场管理</h3>
          <span class="exit-label">私家车出口</span>
        </div>

        <div class="control-section">
          <!-- 车牌号显示和修改 -->
          <div class="license-plate-section">
            <div class="plate-display">
              <button class="plate-prefix" @click="showPrefixModal = true">{{ currentPlatePrefix }}</button>
              <input
                v-model="currentPlateNumber"
                class="plate-number-input"
                placeholder="请输入车牌号"
                maxlength="6"
              />
              <button class="modify-btn" @click="confirmPlateModify">确定修改</button>
            </div>

          </div>

          <!-- 操作按钮区域 -->
          <div class="action-section">
            <div class="action-buttons">
              <button class="action-btn success">入场匹配</button>
              <button class="action-btn info">重新抓拍</button>
              <button class="action-btn primary">入场小票</button>
              <button class="action-btn danger">强制放行</button>
            </div>

            <!-- 车辆类型选择 -->
            <div class="vehicle-type-section">
              <div class="type-info-row">
                <span class="type-label">卡票号:</span>
                <span class="type-value">{{ currentTicketNumber }}</span>
              </div>
              <div class="type-selector">
                <label class="type-label">车辆类型:</label>
                <select v-model="currentVehicleType" class="type-select">
                  <option value="小型车">小型车</option>
                  <option value="大型车">大型车</option>
                  <option value="电单车">电单车</option>
                  <option value="超大型车">超大型车</option>
                  <option value="摩托车">摩托车</option>
                </select>
              </div>
              <div class="type-selector">
                <label class="type-label">车牌类型:</label>
                <select v-model="currentPlateType" class="type-select">
                  <option value="小型车">小型车</option>
                  <option value="大型车">大型车</option>
                  <option value="新能源">新能源</option>
                  <option value="电动车">电动车</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 车牌前缀选择弹窗 -->
    <div v-if="showPrefixModal" class="modal-overlay" @click="showPrefixModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>选择车牌前缀</h3>
          <button class="modal-close" @click="showPrefixModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="prefix-grid">
            <button
              v-for="prefix in allPlatePrefixes"
              :key="prefix.code"
              class="prefix-btn"
              :class="{ active: currentPlatePrefix === prefix.code }"
              @click="selectPlatePrefix(prefix.code)"
            >
              <span class="prefix-code">{{ prefix.code }}</span>
              <span class="prefix-name">{{ prefix.name }}</span>
            </button>
          </div>
        </div>
        <div class="modal-footer">
          <button class="modal-btn cancel" @click="showPrefixModal = false">取消</button>
          <button class="modal-btn confirm" @click="confirmPrefixSelection">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, shallowRef, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import TopMenuBar from '../components/TopMenuBar.vue'
import { ParkingService } from '../services/parkingService'
import { ElectronTester } from '../utils/electronTest'

const router = useRouter()
const parkingService = new ParkingService()

// 响应式数据
const currentTime = ref('')
const selectedVehicle = ref<string | null>(null)
const isRefreshing = ref(false)
const selectedChannel = ref('private-exit')
const showPrefixModal = ref(false)
const showChannelList = ref(false)
const currentPlatePrefix = ref('空')
const currentPlateNumber = ref('MY1234')
const currentTicketNumber = ref('T202401001')
const currentVehicleType = ref('小型车')
const currentPlateType = ref('小型车')

// 停车位数据
const availableParkingSpaces = ref(156) // 剩余车位数

// Electron 相关
const isElectronApp = ref(false)

// 检查是否在 Electron 环境中
onMounted(() => {
  isElectronApp.value = ElectronTester.isElectron()
  if (isElectronApp.value) {
    console.log('🖥️ 检测到 Electron 桌面环境')
  }
})


// 所有车牌前缀数据 - 使用静态数据避免响应式开销
const allPlatePrefixes = [
  { code: '京', name: '北京' },
  { code: '津', name: '天津' },
  { code: '沪', name: '上海' },
  { code: '渝', name: '重庆' },
  { code: '冀', name: '河北' },
  { code: '豫', name: '河南' },
  { code: '云', name: '云南' },
  { code: '辽', name: '辽宁' },
  { code: '黑', name: '黑龙江' },
  { code: '湘', name: '湖南' },
  { code: '皖', name: '安徽' },
  { code: '鲁', name: '山东' },
  { code: '新', name: '新疆' },
  { code: '苏', name: '江苏' },
  { code: '浙', name: '浙江' },
  { code: '赣', name: '江西' },
  { code: '鄂', name: '湖北' },
  { code: '桂', name: '广西' },
  { code: '甘', name: '甘肃' },
  { code: '晋', name: '山西' },
  { code: '蒙', name: '内蒙古' },
  { code: '陕', name: '陕西' },
  { code: '吉', name: '吉林' },
  { code: '闽', name: '福建' },
  { code: '贵', name: '贵州' },
  { code: '粤', name: '广东' },
  { code: '青', name: '青海' },
  { code: '藏', name: '西藏' },
  { code: '川', name: '四川' },
  { code: '宁', name: '宁夏' },
  { code: '琼', name: '海南' },
  { code: '港', name: '香港' },
  { code: '澳', name: '澳门' },
  { code: '台', name: '台湾' },
  { code: '使', name: '使馆' },
  { code: '领', name: '领馆' },
  { code: '电', name: '电动车' },
  { code: '空', name: '空白' }
]

// 使用shallowRef优化对象性能
const hardwareStatus = shallowRef({
  printer: true,
  camera: true,
  cardReader: true,
  groundSensor: false
})

const todayStats = shallowRef({
  totalVehicles: 127,
  currentOccupancy: 45,
  totalRevenue: 1250.50,
  averageStayTime: 85
})

// 当前车辆列表
const currentVehicles = ref([
  {
    id: '1',
    licensePlate: '京A12345',
    vehicleType: 'car',
    entryTime: new Date(Date.now() - 3600000), // 1小时前
    stayDuration: 60,
    amount: 15.50,
    paymentStatus: 'unpaid',
    hasIssue: false
  },
  {
    id: '2',
    licensePlate: '京B67890',
    vehicleType: 'suv',
    entryTime: new Date(Date.now() - 7200000), // 2小时前
    stayDuration: 120,
    amount: 25.00,
    paymentStatus: 'paid',
    hasIssue: false
  },
  {
    id: '3',
    licensePlate: '京C11111',
    vehicleType: 'truck',
    entryTime: new Date(Date.now() - 14400000), // 4小时前
    stayDuration: 240,
    amount: 45.00,
    paymentStatus: 'unpaid',
    hasIssue: true
  }
])

// 计算属性 - 优化性能
const selectedVehicleData = computed(() => {
  if (!selectedVehicle.value) return null
  return currentVehicles.value.find(vehicle => vehicle.id === selectedVehicle.value)
})

// 计算硬件状态总览
const hardwareStatusSummary = computed(() => {
  const status = hardwareStatus.value
  const total = Object.keys(status).length
  const working = Object.values(status).filter(Boolean).length
  return {
    total,
    working,
    percentage: Math.round((working / total) * 100)
  }
})

// 计算今日收入格式化
const formattedRevenue = computed(() => {
  return `¥${todayStats.value.totalRevenue.toFixed(2)}`
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const toggleChannelList = () => {
  showChannelList.value = !showChannelList.value
}

const selectChannel = (channel: string) => {
  selectedChannel.value = channel
  showChannelList.value = false // 选择后自动关闭面板
  console.log('选择通道:', channel)

  // 根据选择的通道更新车牌信息
  updatePlateInfoByChannel(channel)
}

const updatePlateInfoByChannel = (channel: string) => {
  // 模拟根据通道更新车牌信息
  const channelData: Record<string, any> = {
    'private-exit': { prefix: '粤', number: 'B12345', ticket: 'T202401001', type: '小型车' },
    'private-entrance': { prefix: '京', number: 'A88888', ticket: 'T202401002', type: '小型车' },
    'electric-exit': { prefix: '电', number: 'E12345', ticket: 'E202401001', type: '电单车' },
    'b2-exit': { prefix: '沪', number: 'C99999', ticket: 'B202401001', type: '小型车' }
  }

  const data = channelData[channel] || { prefix: '空', number: 'MY1234', ticket: 'T202401001', type: '小型车' }
  currentPlatePrefix.value = data.prefix
  currentPlateNumber.value = data.number
  currentTicketNumber.value = data.ticket
  currentVehicleType.value = data.type
}

const confirmPlateModify = () => {
  console.log('确认修改车牌:', currentPlatePrefix.value + currentPlateNumber.value)
}

const selectPlatePrefix = (prefix: string) => {
  const startTime = performance.now()
  currentPlatePrefix.value = prefix
  // 立即关闭弹窗，减少用户操作步骤
  showPrefixModal.value = false
  const endTime = performance.now()
  console.log('选择车牌前缀:', prefix, `耗时: ${endTime - startTime}ms`)
}

const confirmPrefixSelection = () => {
  showPrefixModal.value = false
  console.log('确认车牌前缀:', currentPlatePrefix.value)
}

// Electron 功能测试
const testElectronFeatures = async () => {
  console.log('🚀 开始测试 Electron 桌面功能...')
  await ElectronTester.runFullTest()
}

const selectVehicle = (vehicleId: string) => {
  selectedVehicle.value = vehicleId
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  }
  return `${mins}分钟`
}

const getVehicleTypeText = (type: string) => {
  const types: Record<string, string> = {
    car: '小型车',
    suv: 'SUV',
    truck: '货车',
    motorcycle: '摩托车'
  }
  return types[type] || '未知'
}

const getPaymentStatusText = (status: string) => {
  const statuses: Record<string, string> = {
    paid: '已付费',
    unpaid: '未付费',
    processing: '处理中'
  }
  return statuses[status] || '未知'
}

const refreshData = async () => {
  isRefreshing.value = true
  try {
    console.log('刷新数据')
    const stats = await parkingService.getTodayStats()
    todayStats.value = stats
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    isRefreshing.value = false
  }
}

const showSettings = () => {
  router.push('/settings')
}

const checkHardware = () => {
  console.log('检测硬件')
  // 模拟硬件检测
  hardwareStatus.value.printer = Math.random() > 0.2
  hardwareStatus.value.camera = Math.random() > 0.1
  hardwareStatus.value.cardReader = Math.random() > 0.15
  hardwareStatus.value.groundSensor = Math.random() > 0.3
}

const manualEntry = () => {
  router.push('/parking')
}

const processPayment = () => {
  router.push('/payment')
}

const viewReports = () => {
  router.push('/reports')
}

const emergencyOpen = () => {
  console.log('紧急开闸')
  alert('紧急开闸已执行')
}

// 生命周期
onMounted(() => {
  // 更新时间
  updateTime()
  const timeInterval = setInterval(updateTime, 1000)

  // 清理定时器
  onUnmounted(() => {
    clearInterval(timeInterval)
  })
})
</script>

<style scoped>
@import '../styles/homepage-layout.css';

/* 自定义样式 */
.system-overview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.overview-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.overview-title {
  font-size: 2em;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.overview-subtitle {
  font-size: 1.2em;
  color: #666;
  margin-bottom: 30px;
}

.system-stats {
  display: flex;
  gap: 40px;
}

.system-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 1.8em;
  font-weight: bold;
  color: #1976d2;
}

.stat-text {
  font-size: 0.9em;
  color: #666;
  margin-top: 5px;
}

.dashboard-overview {
  padding: 20px;
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.vehicle-type-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 600;
}

.vehicle-type-badge.car {
  background: #e3f2fd;
  color: #1976d2;
}

.vehicle-type-badge.suv {
  background: #f3e5f5;
  color: #7b1fa2;
}

.vehicle-type-badge.truck {
  background: #fff3e0;
  color: #f57c00;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-card .stat-label {
  font-size: 0.9em;
  color: #666;
  margin-bottom: 8px;
}

.stat-card .stat-value {
  font-size: 1.4em;
  font-weight: bold;
  color: #333;
}

.stat-card .stat-value.amount {
  color: #4caf50;
}

.stat-card .stat-value.paid {
  color: #4caf50;
}

.stat-card .stat-value.unpaid {
  color: #f44336;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background: #f8f9fa;
}

.device-item.online {
  border-left: 4px solid #4caf50;
}

.device-item.offline {
  border-left: 4px solid #f44336;
}

.device-icon {
  margin-right: 8px;
  font-size: 1.2em;
}

.device-name {
  flex: 1;
  font-weight: 500;
}

.device-status {
  font-size: 0.9em;
  font-weight: 600;
}

.device-item.online .device-status {
  color: #4caf50;
}

.device-item.offline .device-status {
  color: #f44336;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #1976d2;
  color: white;
}

.action-btn.primary:hover {
  background: #1565c0;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #333;
}

.action-btn.secondary:hover {
  background: #e0e0e0;
}

.action-btn.warning {
  background: #ff9800;
  color: white;
}

.action-btn.warning:hover {
  background: #f57c00;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-item {
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item .stat-value {
  font-size: 1.2em;
  font-weight: bold;
  color: #1976d2;
}

.stat-item .stat-label {
  font-size: 0.8em;
  color: #666;
  margin-top: 4px;
}

/* Electron 测试按钮样式 */
.electron-test-btn {
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.electron-test-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.electron-test-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
