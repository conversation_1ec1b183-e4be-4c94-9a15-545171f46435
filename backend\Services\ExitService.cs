using ParkingBoothApi.DTOs;
using ParkingBoothApi.Models;
using ParkingBoothApi.Interfaces;

namespace ParkingBoothApi.Services
{
    public class ExitService : IExitService
    {
        private readonly ILogger<ExitService> _logger;
        private readonly IParkingService _parkingService;
        private readonly IHardwareService _hardwareService;
        
        private static readonly List<ExitVehicleDetectionDto> _exitDetections = new();
        private static readonly List<ExitStatusDto> _exits = new();
        private static readonly List<CouponDto> _availableCoupons = new();
        private static readonly Dictionary<string, ShiftSummaryDto> _activeShifts = new();
        private static readonly Dictionary<string, bool> _exitBarrierStates = new();

        public ExitService(
            ILogger<ExitService> logger, 
            IParkingService parkingService,
            IHardwareService hardwareService)
        {
            _logger = logger;
            _parkingService = parkingService;
            _hardwareService = hardwareService;
            InitializeExits();
            InitializeCoupons();
        }

        private void InitializeExits()
        {
            var exitIds = new[] { "exit-1", "exit-2", "exit-b2", "exit-truck" };
            var exitNames = new[] { "私家车出口1", "私家车出口2", "B2出口", "货车出口" };

            for (int i = 0; i < exitIds.Length; i++)
            {
                _exits.Add(new ExitStatusDto
                {
                    ExitId = exitIds[i],
                    Name = exitNames[i],
                    IsOnline = true,
                    IsBarrierOpen = false,
                    IsCameraWorking = true,
                    IsGroundSensorWorking = true,
                    IsPrinterWorking = true,
                    IsQrScannerWorking = true,
                    QueueLength = 0,
                    LastActivity = DateTime.UtcNow
                });
                _exitBarrierStates[exitIds[i]] = false;
            }
        }

        private void InitializeCoupons()
        {
            // 模拟一些可用的抵用券
            _availableCoupons.AddRange(new[]
            {
                new CouponDto { Id = "CASH001", Type = CouponType.CashCoupon, Value = 10, Code = "CASH10", ExpiryDate = DateTime.UtcNow.AddMonths(1) },
                new CouponDto { Id = "HOUR001", Type = CouponType.HourCoupon, Value = 2, Code = "HOUR2", ExpiryDate = DateTime.UtcNow.AddMonths(1) },
                new CouponDto { Id = "FREE001", Type = CouponType.FreeCoupon, Value = 0, Code = "FREE", ExpiryDate = DateTime.UtcNow.AddMonths(1) }
            });
        }

        public async Task<ExitVehicleDetectionDto> ProcessVehicleDetectionAsync(ExitVehicleDetectionDto detection)
        {
            _logger.LogInformation("Processing exit detection: {LicensePlate} at {ExitId}", 
                detection.LicensePlate, detection.ExitId);

            detection.Id = Guid.NewGuid().ToString();
            detection.DetectedAt = DateTime.UtcNow;
            detection.Status = ExitVehicleStatus.Processing;

            // 查找匹配的入场记录
            var matchedVehicle = await FindMatchingVehicleAsync(detection.LicensePlate);
            if (matchedVehicle != null)
            {
                detection.MatchedVehicle = matchedVehicle;
                detection.ParkingDuration = DateTime.UtcNow - matchedVehicle.EntryTime;
                detection.CalculatedFee = await CalculateParkingFeeAsync(matchedVehicle, detection.ParkingDuration);
            }

            _exitDetections.Add(detection);

            // 更新出口状态
            var exit = _exits.FirstOrDefault(e => e.ExitId == detection.ExitId);
            if (exit != null)
            {
                exit.LastActivity = DateTime.UtcNow;
                exit.PendingVehicles.Add(detection);
                exit.QueueLength = exit.PendingVehicles.Count;
            }

            // 自动决策是否可以出场
            var decision = await MakeExitDecisionAsync(detection);
            
            if (decision.CanExit && !decision.RequiresManualIntervention)
            {
                // 自动处理出场
                await ProcessAutomaticExitAsync(detection, decision);
            }

            return detection;
        }

        public async Task<ExitDecisionDto> MakeExitDecisionAsync(ExitVehicleDetectionDto detection)
        {
            var decision = new ExitDecisionDto
            {
                DetectionId = detection.Id,
                ProcessedAt = DateTime.UtcNow
            };

            var blockingReasons = new List<string>();

            if (detection.MatchedVehicle == null)
            {
                // 没有找到匹配的入场记录
                blockingReasons.Add("未找到匹配的入场记录");
                decision.RequiresManualIntervention = true;
                
                // 提供可能的匹配建议
                decision.SuggestedMatches = await GetSuggestedMatchesAsync(detection.LicensePlate);
            }
            else
            {
                // 计算费用
                decision.TotalFee = detection.CalculatedFee;
                decision.FinalAmount = decision.TotalFee;

                // 检查是否为免费车辆（月卡、VIP等）
                if (await IsVipVehicleAsync(detection.LicensePlate))
                {
                    decision.FinalAmount = 0;
                    decision.RequiredPaymentMethod = PaymentMethod.Free;
                }
                else if (decision.TotalFee > 0)
                {
                    decision.RequiredPaymentMethod = PaymentMethod.Cash; // 默认现金支付
                    blockingReasons.Add($"需要支付停车费: ¥{decision.TotalFee:F2}");
                }
            }

            // 检查识别置信度
            if (detection.Confidence < 0.8)
            {
                blockingReasons.Add("车牌识别置信度过低");
                decision.RequiresManualIntervention = true;
            }

            decision.CanExit = blockingReasons.Count == 0 || decision.FinalAmount == 0;
            decision.BlockingReasons = blockingReasons;
            decision.Reason = decision.CanExit ? "允许出场" : string.Join(", ", blockingReasons);

            return decision;
        }

        public async Task<ExitProcessResultDto> ProcessPaymentAsync(PaymentProcessDto paymentDto)
        {
            _logger.LogInformation("Processing payment: {Method} for detection {DetectionId}", 
                paymentDto.PaymentMethod, paymentDto.DetectionId);

            var detection = _exitDetections.FirstOrDefault(d => d.Id == paymentDto.DetectionId);
            if (detection == null)
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = "未找到对应的车辆检测记录"
                };
            }

            try
            {
                // 处理不同的支付方式
                var result = paymentDto.PaymentMethod switch
                {
                    PaymentMethod.Cash => await ProcessCashPaymentAsync(detection, paymentDto),
                    PaymentMethod.QrCode => await ProcessQrPaymentAsync(detection, paymentDto),
                    PaymentMethod.Coupon => await ProcessCouponPaymentAsync(detection, paymentDto),
                    PaymentMethod.Free => await ProcessFreeExitAsync(detection, paymentDto),
                    PaymentMethod.Mixed => await ProcessMixedPaymentAsync(detection, paymentDto),
                    _ => throw new NotSupportedException($"不支持的支付方式: {paymentDto.PaymentMethod}")
                };

                if (result.Success)
                {
                    // 记录交易
                    var transaction = await RecordExitTransactionAsync(detection, paymentDto);
                    result.Transaction = transaction;

                    // 自动开闸
                    await ControlExitBarrierAsync(detection.ExitId, BarrierAction.Open, paymentDto.OperatorId, "支付完成自动开闸");

                    // 更新车辆状态
                    if (detection.MatchedVehicle != null)
                    {
                        await _parkingService.RecordVehicleExitAsync(new VehicleExitDto
                        {
                            LicensePlate = detection.MatchedVehicle.LicensePlate,
                            ExitTime = DateTime.UtcNow,
                            PaidAmount = paymentDto.Amount,
                            PaymentMethod = paymentDto.PaymentMethod.ToString()
                        });
                    }

                    // 生成小票数据
                    if (paymentDto.PaymentMethod == PaymentMethod.Cash)
                    {
                        result.ReceiptData = await GenerateReceiptDataAsync(detection, paymentDto);
                    }

                    // 更新班次统计
                    await UpdateShiftSummaryAsync(paymentDto.OperatorId, paymentDto.PaymentMethod, paymentDto.Amount);

                    detection.Status = ExitVehicleStatus.PaymentCompleted;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "支付处理失败: {DetectionId}", paymentDto.DetectionId);
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = $"支付处理失败: {ex.Message}"
                };
            }
        }

        public async Task<ExitProcessResultDto> ProcessTicketExitAsync(TicketExitDto ticketDto)
        {
            _logger.LogInformation("Processing ticket exit: {TicketCode} at {ExitId}", 
                ticketDto.TicketCode, ticketDto.ExitId);

            try
            {
                // 验证票据并获取车辆信息
                var vehicleInfo = await ValidateAndGetTicketInfoAsync(ticketDto.TicketCode);
                if (vehicleInfo == null)
                {
                    return new ExitProcessResultDto
                    {
                        Success = false,
                        Message = "无效的票据或票据已过期"
                    };
                }

                // 创建虚拟检测记录
                var detection = new ExitVehicleDetectionDto
                {
                    LicensePlate = vehicleInfo.LicensePlate,
                    VehicleType = vehicleInfo.VehicleType,
                    ExitId = ticketDto.ExitId,
                    Confidence = 1.0,
                    Status = ExitVehicleStatus.AwaitingPayment,
                    MatchedVehicle = vehicleInfo
                };

                detection.ParkingDuration = DateTime.UtcNow - vehicleInfo.EntryTime;
                detection.CalculatedFee = await CalculateParkingFeeAsync(vehicleInfo, detection.ParkingDuration);

                _exitDetections.Add(detection);

                return new ExitProcessResultDto
                {
                    Success = true,
                    Message = "票据验证成功，请进行支付",
                    Decision = await MakeExitDecisionAsync(detection)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "票据出场处理失败: {TicketCode}", ticketDto.TicketCode);
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = $"票据出场处理失败: {ex.Message}"
                };
            }
        }

        public async Task<ExitProcessResultDto> ProcessCardExitAsync(CardExitDto cardDto)
        {
            _logger.LogInformation("Processing card exit: {CardId} at {ExitId}", 
                cardDto.CardId, cardDto.ExitId);

            try
            {
                // 验证卡片并查找对应的入场记录
                var vehicleInfo = await FindVehicleByCardAsync(cardDto.CardId);
                if (vehicleInfo == null)
                {
                    return new ExitProcessResultDto
                    {
                        Success = false,
                        Message = "未找到对应的入场记录或卡片无效"
                    };
                }

                // 创建检测记录
                var detection = new ExitVehicleDetectionDto
                {
                    LicensePlate = vehicleInfo.LicensePlate,
                    VehicleType = vehicleInfo.VehicleType,
                    ExitId = cardDto.ExitId,
                    Confidence = 1.0,
                    Status = ExitVehicleStatus.Processing,
                    MatchedVehicle = vehicleInfo
                };

                detection.ParkingDuration = DateTime.UtcNow - vehicleInfo.EntryTime;
                detection.CalculatedFee = await CalculateParkingFeeAsync(vehicleInfo, detection.ParkingDuration);

                _exitDetections.Add(detection);

                // 自动决策
                var decision = await MakeExitDecisionAsync(detection);
                
                if (decision.CanExit && decision.FinalAmount == 0)
                {
                    // 免费出场
                    return await ProcessFreeExitAsync(detection, new PaymentProcessDto
                    {
                        DetectionId = detection.Id,
                        PaymentMethod = PaymentMethod.Free,
                        Amount = 0,
                        OperatorId = "SYSTEM"
                    });
                }

                return new ExitProcessResultDto
                {
                    Success = true,
                    Message = "刷卡成功，请进行支付",
                    Decision = decision
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷卡出场处理失败: {CardId}", cardDto.CardId);
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = $"刷卡出场处理失败: {ex.Message}"
                };
            }
        }

        public async Task<ExitProcessResultDto> ProcessManualActionAsync(ManualExitActionDto actionDto)
        {
            _logger.LogInformation("Processing manual exit action: {Action} for detection {DetectionId}", 
                actionDto.Action, actionDto.DetectionId);

            var detection = _exitDetections.FirstOrDefault(d => d.Id == actionDto.DetectionId);
            if (detection == null)
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = "未找到对应的车辆检测记录"
                };
            }

            return actionDto.Action switch
            {
                ExitAction.ForceExit => await ProcessForceExitAsync(detection, actionDto),
                ExitAction.ManualMatch => await ProcessManualMatchAsync(detection, actionDto),
                ExitAction.ManualEntry => await ProcessManualEntryAsync(detection, actionDto),
                ExitAction.Recapture => await ProcessRecaptureAsync(detection, actionDto),
                ExitAction.Reject => await ProcessRejectExitAsync(detection, actionDto),
                ExitAction.FreeExit => await ProcessFreeExitAsync(detection, new PaymentProcessDto
                {
                    DetectionId = detection.Id,
                    PaymentMethod = PaymentMethod.Free,
                    Amount = 0,
                    OperatorId = actionDto.OperatorId,
                    Remarks = actionDto.Reason
                }),
                _ => new ExitProcessResultDto
                {
                    Success = false,
                    Message = "不支持的操作类型"
                }
            };
        }

        public async Task<bool> ControlExitBarrierAsync(string exitId, BarrierAction action, string operatorId, string reason)
        {
            _logger.LogInformation("Controlling exit barrier: {Action} at {ExitId}", action, exitId);

            var exit = _exits.FirstOrDefault(e => e.ExitId == exitId);
            if (exit == null)
            {
                return false;
            }

            switch (action)
            {
                case BarrierAction.Open:
                    exit.IsBarrierOpen = true;
                    _exitBarrierStates[exitId] = true;
                    // 模拟5秒后自动关闭
                    _ = Task.Delay(5000).ContinueWith(_ => {
                        exit.IsBarrierOpen = false;
                        _exitBarrierStates[exitId] = false;
                    });
                    break;
                case BarrierAction.Close:
                    exit.IsBarrierOpen = false;
                    _exitBarrierStates[exitId] = false;
                    break;
                case BarrierAction.Reset:
                    exit.IsBarrierOpen = false;
                    _exitBarrierStates[exitId] = false;
                    break;
                case BarrierAction.Emergency:
                    exit.IsBarrierOpen = true;
                    _exitBarrierStates[exitId] = true;
                    break;
            }

            exit.LastActivity = DateTime.UtcNow;
            return true;
        }

        public async Task<List<ExitStatusDto>> GetExitStatusAsync()
        {
            return _exits.ToList();
        }

        public async Task<ShiftSummaryDto> GetCurrentShiftSummaryAsync(string operatorId)
        {
            if (_activeShifts.TryGetValue(operatorId, out var shift))
            {
                return shift;
            }

            // 创建新班次
            var newShift = new ShiftSummaryDto
            {
                ShiftId = Guid.NewGuid().ToString(),
                OperatorId = operatorId,
                OperatorName = $"操作员{operatorId}",
                ShiftStart = DateTime.UtcNow
            };

            _activeShifts[operatorId] = newShift;
            return newShift;
        }

        public async Task<ParkingSpaceInfoDto> GetParkingSpaceInfoAsync()
        {
            var totalSpaces = 500; // 模拟总车位数
            var occupiedSpaces = await _parkingService.GetCurrentOccupancyAsync();
            var availableSpaces = totalSpaces - occupiedSpaces;

            return new ParkingSpaceInfoDto
            {
                TotalSpaces = totalSpaces,
                OccupiedSpaces = occupiedSpaces,
                AvailableSpaces = availableSpaces,
                OccupancyRate = (double)occupiedSpaces / totalSpaces * 100,
                ZoneBreakdown = new List<ZoneSpaceInfoDto>
                {
                    new() { ZoneId = "A", ZoneName = "A区", TotalSpaces = 150, OccupiedSpaces = occupiedSpaces / 3, AvailableSpaces = 150 - occupiedSpaces / 3 },
                    new() { ZoneId = "B", ZoneName = "B区", TotalSpaces = 200, OccupiedSpaces = occupiedSpaces / 2, AvailableSpaces = 200 - occupiedSpaces / 2 },
                    new() { ZoneId = "C", ZoneName = "C区", TotalSpaces = 150, OccupiedSpaces = occupiedSpaces / 6, AvailableSpaces = 150 - occupiedSpaces / 6 }
                },
                LastUpdated = DateTime.UtcNow
            };
        }

        public async Task<List<CouponDto>> GetAvailableCouponsAsync()
        {
            return _availableCoupons.Where(c => !c.IsUsed && c.ExpiryDate > DateTime.UtcNow).ToList();
        }

        // 私有辅助方法
        private async Task<Vehicle?> FindMatchingVehicleAsync(string licensePlate)
        {
            var vehicles = await _parkingService.GetParkedVehiclesAsync();
            return vehicles.FirstOrDefault(v =>
                v.LicensePlate.Equals(licensePlate, StringComparison.OrdinalIgnoreCase) &&
                v.Status == VehicleStatus.Parked);
        }

        private async Task<decimal> CalculateParkingFeeAsync(Vehicle vehicle, TimeSpan duration)
        {
            // 简化的费用计算逻辑
            var hours = Math.Ceiling(duration.TotalHours);
            var hourlyRate = vehicle.VehicleType switch
            {
                VehicleType.Car => 5.0m,
                VehicleType.Motorcycle => 2.0m,
                VehicleType.Truck => 10.0m,
                _ => 5.0m
            };

            var totalFee = (decimal)hours * hourlyRate;
            return Math.Max(totalFee, 5.0m); // 最低收费5元
        }

        private async Task<List<Vehicle>> GetSuggestedMatchesAsync(string licensePlate)
        {
            var vehicles = await _parkingService.GetParkedVehiclesAsync();

            // 模糊匹配逻辑
            return vehicles.Where(v =>
                v.LicensePlate.Contains(licensePlate.Substring(0, Math.Min(3, licensePlate.Length))) ||
                licensePlate.Contains(v.LicensePlate.Substring(0, Math.Min(3, v.LicensePlate.Length))))
                .Take(5)
                .ToList();
        }

        private async Task<bool> IsVipVehicleAsync(string licensePlate)
        {
            // 模拟VIP车辆检查
            var vipPlates = new[] { "VIP001", "VIP002", "月卡001", "粤B12345" };
            return vipPlates.Contains(licensePlate);
        }

        private async Task<ExitProcessResultDto> ProcessAutomaticExitAsync(ExitVehicleDetectionDto detection, ExitDecisionDto decision)
        {
            if (decision.FinalAmount == 0)
            {
                // 免费出场
                return await ProcessFreeExitAsync(detection, new PaymentProcessDto
                {
                    DetectionId = detection.Id,
                    PaymentMethod = PaymentMethod.Free,
                    Amount = 0,
                    OperatorId = "SYSTEM"
                });
            }

            // 需要支付，等待人工处理
            detection.Status = ExitVehicleStatus.AwaitingPayment;
            return new ExitProcessResultDto
            {
                Success = true,
                Message = "车辆检测成功，等待支付",
                Decision = decision
            };
        }

        private async Task<ExitProcessResultDto> ProcessCashPaymentAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            // 验证支付金额
            if (paymentDto.Amount < detection.CalculatedFee)
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = $"支付金额不足，应付: ¥{detection.CalculatedFee:F2}，实付: ¥{paymentDto.Amount:F2}"
                };
            }

            return new ExitProcessResultDto
            {
                Success = true,
                Message = "现金支付成功"
            };
        }

        private async Task<ExitProcessResultDto> ProcessQrPaymentAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            // 模拟二维码支付验证
            if (string.IsNullOrEmpty(paymentDto.QrCodeData))
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = "二维码数据无效"
                };
            }

            // 模拟支付成功
            return new ExitProcessResultDto
            {
                Success = true,
                Message = "扫码支付成功"
            };
        }

        private async Task<ExitProcessResultDto> ProcessCouponPaymentAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            decimal totalCouponValue = 0;
            var usedCoupons = new List<CouponDto>();

            foreach (var coupon in paymentDto.UsedCoupons)
            {
                var availableCoupon = _availableCoupons.FirstOrDefault(c => c.Id == coupon.Id && !c.IsUsed);
                if (availableCoupon != null)
                {
                    totalCouponValue += availableCoupon.Value;
                    availableCoupon.IsUsed = true;
                    usedCoupons.Add(availableCoupon);
                }
            }

            if (totalCouponValue < detection.CalculatedFee)
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = $"抵用券金额不足，应付: ¥{detection.CalculatedFee:F2}，券值: ¥{totalCouponValue:F2}"
                };
            }

            return new ExitProcessResultDto
            {
                Success = true,
                Message = "抵用券支付成功"
            };
        }

        private async Task<ExitProcessResultDto> ProcessMixedPaymentAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            // 混合支付逻辑（现金+券+扫码等）
            return new ExitProcessResultDto
            {
                Success = true,
                Message = "混合支付成功"
            };
        }

        private async Task<ExitProcessResultDto> ProcessFreeExitAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            return new ExitProcessResultDto
            {
                Success = true,
                Message = "免费放行成功"
            };
        }

        private async Task<ExitProcessResultDto> ProcessForceExitAsync(ExitVehicleDetectionDto detection, ManualExitActionDto actionDto)
        {
            // 强制放行需要特殊权限验证
            return new ExitProcessResultDto
            {
                Success = true,
                Message = "强制放行成功"
            };
        }

        private async Task<ExitProcessResultDto> ProcessManualMatchAsync(ExitVehicleDetectionDto detection, ManualExitActionDto actionDto)
        {
            if (string.IsNullOrEmpty(actionDto.SelectedVehicleId))
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = "请选择要匹配的车辆"
                };
            }

            // 手动匹配车辆
            var vehicles = await _parkingService.GetParkedVehiclesAsync();
            var selectedVehicle = vehicles.FirstOrDefault(v => v.Id == actionDto.SelectedVehicleId);

            if (selectedVehicle != null)
            {
                detection.MatchedVehicle = selectedVehicle;
                detection.ParkingDuration = DateTime.UtcNow - selectedVehicle.EntryTime;
                detection.CalculatedFee = await CalculateParkingFeeAsync(selectedVehicle, detection.ParkingDuration);
                detection.Status = ExitVehicleStatus.AwaitingPayment;
            }

            return new ExitProcessResultDto
            {
                Success = true,
                Message = "手动匹配成功",
                Decision = await MakeExitDecisionAsync(detection)
            };
        }

        private async Task<ExitProcessResultDto> ProcessManualEntryAsync(ExitVehicleDetectionDto detection, ManualExitActionDto actionDto)
        {
            if (!actionDto.ManualEntryTime.HasValue)
            {
                return new ExitProcessResultDto
                {
                    Success = false,
                    Message = "请输入入场时间"
                };
            }

            // 创建虚拟入场记录
            var virtualVehicle = new Vehicle
            {
                Id = Guid.NewGuid().ToString(),
                LicensePlate = actionDto.ModifiedLicensePlate ?? detection.LicensePlate,
                VehicleType = actionDto.ModifiedVehicleType ?? detection.VehicleType,
                EntryTime = actionDto.ManualEntryTime.Value,
                Status = VehicleStatus.Parked
            };

            detection.MatchedVehicle = virtualVehicle;
            detection.ParkingDuration = DateTime.UtcNow - virtualVehicle.EntryTime;
            detection.CalculatedFee = await CalculateParkingFeeAsync(virtualVehicle, detection.ParkingDuration);
            detection.Status = ExitVehicleStatus.AwaitingPayment;

            return new ExitProcessResultDto
            {
                Success = true,
                Message = "手动补录成功",
                Decision = await MakeExitDecisionAsync(detection)
            };
        }

        private async Task<ExitProcessResultDto> ProcessRecaptureAsync(ExitVehicleDetectionDto detection, ManualExitActionDto actionDto)
        {
            // 模拟重新抓拍
            detection.Confidence = Math.Min(1.0, detection.Confidence + 0.1);
            detection.DetectedAt = DateTime.UtcNow;
            detection.Status = ExitVehicleStatus.Processing;

            return new ExitProcessResultDto
            {
                Success = true,
                Message = "重新抓拍完成",
                Decision = await MakeExitDecisionAsync(detection)
            };
        }

        private async Task<ExitProcessResultDto> ProcessRejectExitAsync(ExitVehicleDetectionDto detection, ManualExitActionDto actionDto)
        {
            detection.Status = ExitVehicleStatus.Rejected;

            return new ExitProcessResultDto
            {
                Success = true,
                Message = "车辆出场已拒绝"
            };
        }

        private async Task<ParkingTransactionDto> RecordExitTransactionAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            var exitDto = new VehicleExitDto
            {
                LicensePlate = detection.LicensePlate,
                ExitTime = DateTime.UtcNow,
                PaidAmount = paymentDto.Amount,
                PaymentMethod = paymentDto.PaymentMethod.ToString()
            };

            return await _parkingService.RecordVehicleExitAsync(exitDto);
        }

        private async Task<string> GenerateReceiptDataAsync(ExitVehicleDetectionDto detection, PaymentProcessDto paymentDto)
        {
            var receipt = new PrintReceiptDto
            {
                TransactionId = Guid.NewGuid().ToString(),
                LicensePlate = detection.LicensePlate,
                EntryTime = detection.MatchedVehicle?.EntryTime ?? DateTime.UtcNow,
                ExitTime = DateTime.UtcNow,
                ParkingDuration = detection.ParkingDuration,
                TotalFee = detection.CalculatedFee,
                PaidAmount = paymentDto.Amount,
                PaymentMethod = paymentDto.PaymentMethod,
                OperatorName = $"操作员{paymentDto.OperatorId}"
            };

            // 生成打印数据（简化版）
            return $"停车收费小票\n" +
                   $"车牌号: {receipt.LicensePlate}\n" +
                   $"入场时间: {receipt.EntryTime:yyyy-MM-dd HH:mm:ss}\n" +
                   $"出场时间: {receipt.ExitTime:yyyy-MM-dd HH:mm:ss}\n" +
                   $"停车时长: {receipt.ParkingDuration.Hours}小时{receipt.ParkingDuration.Minutes}分钟\n" +
                   $"应收金额: ¥{receipt.TotalFee:F2}\n" +
                   $"实收金额: ¥{receipt.PaidAmount:F2}\n" +
                   $"支付方式: {receipt.PaymentMethod}\n" +
                   $"操作员: {receipt.OperatorName}\n" +
                   $"时间: {receipt.PrintTime:yyyy-MM-dd HH:mm:ss}";
        }

        private async Task UpdateShiftSummaryAsync(string operatorId, PaymentMethod paymentMethod, decimal amount)
        {
            var shift = await GetCurrentShiftSummaryAsync(operatorId);

            shift.TotalVehiclesProcessed++;
            shift.TotalRevenue += amount;

            switch (paymentMethod)
            {
                case PaymentMethod.Cash:
                    shift.TotalCashReceived += amount;
                    break;
                case PaymentMethod.QrCode:
                    shift.TotalQrPayments += amount;
                    break;
                case PaymentMethod.Coupon:
                    shift.TotalCouponValue += amount;
                    break;
                case PaymentMethod.Free:
                    shift.FreeExits++;
                    break;
            }

            // 更新支付方式统计
            var breakdown = shift.PaymentBreakdown.FirstOrDefault(b => b.Method == paymentMethod);
            if (breakdown == null)
            {
                breakdown = new PaymentBreakdownDto { Method = paymentMethod, Count = 0, Amount = 0 };
                shift.PaymentBreakdown.Add(breakdown);
            }
            breakdown.Count++;
            breakdown.Amount += amount;
        }

        private async Task<Vehicle?> ValidateAndGetTicketInfoAsync(string ticketCode)
        {
            // 模拟票据验证逻辑
            var vehicles = await _parkingService.GetParkedVehiclesAsync();
            return vehicles.FirstOrDefault(v => v.Id.EndsWith(ticketCode.Substring(Math.Max(0, ticketCode.Length - 4))));
        }

        private async Task<Vehicle?> FindVehicleByCardAsync(string cardId)
        {
            // 模拟通过卡片查找车辆
            var vehicles = await _parkingService.GetParkedVehiclesAsync();
            return vehicles.FirstOrDefault(v => v.LicensePlate.Contains(cardId.Substring(Math.Max(0, cardId.Length - 3))));
        }
    }
}
