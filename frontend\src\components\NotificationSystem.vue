<template>
  <div class="notification-container">
    <transition-group name="notification" tag="div">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="['notification', `notification-${notification.type}`]"
        @click="removeNotification(notification.id)"
      >
        <div class="notification-icon">
          <span v-if="notification.type === 'success'">✅</span>
          <span v-else-if="notification.type === 'error'">❌</span>
          <span v-else-if="notification.type === 'warning'">⚠️</span>
          <span v-else-if="notification.type === 'info'">ℹ️</span>
        </div>
        
        <div class="notification-content">
          <div class="notification-title" v-if="notification.title">
            {{ notification.title }}
          </div>
          <div class="notification-message">
            {{ notification.message }}
          </div>
        </div>
        
        <button class="notification-close" @click.stop="removeNotification(notification.id)">
          ✕
        </button>
      </div>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Notification {
  id: number
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
}

const notifications = ref<Notification[]>([])
let notificationId = 0

const addNotification = (notification: Omit<Notification, 'id'>) => {
  const id = ++notificationId
  const newNotification: Notification = {
    id,
    duration: 4000,
    ...notification
  }
  
  notifications.value.push(newNotification)
  
  // 自动移除通知
  if (newNotification.duration && newNotification.duration > 0) {
    setTimeout(() => {
      removeNotification(id)
    }, newNotification.duration)
  }
  
  return id
}

const removeNotification = (id: number) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const clearAll = () => {
  notifications.value = []
}

// 暴露方法给外部使用
defineExpose({
  addNotification,
  removeNotification,
  clearAll,
  success: (message: string, title?: string) => addNotification({ type: 'success', message, title }),
  error: (message: string, title?: string) => addNotification({ type: 'error', message, title }),
  warning: (message: string, title?: string) => addNotification({ type: 'warning', message, title }),
  info: (message: string, title?: string) => addNotification({ type: 'info', message, title })
})
</script>

<style scoped>
/* 停车收费系统专业通知样式 */
.notification-container {
  position: fixed;
  top: 80px; /* 避开顶部菜单栏 */
  right: 20px;
  z-index: 300000;
  pointer-events: none;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-width: 350px;
  max-width: 420px;
  padding: 16px 20px;
  margin-bottom: 8px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  border-left: 4px solid;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notification:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 停车系统专业配色方案 */
.notification-success {
  border-left-color: #28a745;
  background: #ffffff;
}

.notification-success .notification-icon {
  color: #28a745;
}

.notification-error {
  border-left-color: #dc3545;
  background: #ffffff;
}

.notification-error .notification-icon {
  color: #dc3545;
}

.notification-warning {
  border-left-color: #ffc107;
  background: #ffffff;
}

.notification-warning .notification-icon {
  color: #856404;
}

.notification-info {
  border-left-color: #4a90e2;
  background: #ffffff;
}

.notification-info .notification-icon {
  color: #4a90e2;
}

.notification-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 1px;
  opacity: 0.9;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #495057;
  line-height: 1.5;
  word-wrap: break-word;
}

.notification-close {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 16px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  line-height: 1;
}

.notification-close:hover {
  background: #f8f9fa;
  color: #495057;
}

/* 专业动画效果 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.25s ease-in;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.25s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-container {
    top: 70px;
    right: 10px;
    left: 10px;
  }

  .notification {
    min-width: auto;
    max-width: none;
    padding: 14px 16px;
  }

  .notification-title {
    font-size: 13px;
  }

  .notification-message {
    font-size: 12px;
  }
}

/* 工业级系统适配 */
@media (min-width: 1200px) {
  .notification {
    min-width: 380px;
    max-width: 450px;
  }
}
</style>
