using ParkingBoothApi.Models;

namespace ParkingBoothApi.DTOs
{
    // 出口车辆检测相关 DTO
    public class ExitVehicleDetectionDto
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string LicensePlate { get; set; } = string.Empty;
        public VehicleType VehicleType { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
        public double Confidence { get; set; }
        public string ExitId { get; set; } = string.Empty;
        public ExitVehicleStatus Status { get; set; }
        public Vehicle? MatchedVehicle { get; set; }
        public decimal CalculatedFee { get; set; }
        public TimeSpan ParkingDuration { get; set; }
    }

    // 出场决策结果 DTO
    public class ExitDecisionDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public bool CanExit { get; set; }
        public string Reason { get; set; } = string.Empty;
        public List<string> BlockingReasons { get; set; } = new();
        public decimal TotalFee { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public PaymentMethod RequiredPaymentMethod { get; set; }
        public bool RequiresManualIntervention { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
        public List<Vehicle> SuggestedMatches { get; set; } = new();
    }

    // 收费处理 DTO
    public class PaymentProcessDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public PaymentMethod PaymentMethod { get; set; }
        public decimal Amount { get; set; }
        public string? QrCodeData { get; set; }
        public string? TicketCode { get; set; }
        public List<CouponDto> UsedCoupons { get; set; } = new();
        public string OperatorId { get; set; } = string.Empty;
        public string? Remarks { get; set; }
        public DateTime PaymentTime { get; set; } = DateTime.UtcNow;
    }

    // 抵用券 DTO
    public class CouponDto
    {
        public string Id { get; set; } = string.Empty;
        public CouponType Type { get; set; }
        public decimal Value { get; set; }
        public string Code { get; set; } = string.Empty;
        public DateTime ExpiryDate { get; set; }
        public bool IsUsed { get; set; }
    }

    // 验票出场 DTO
    public class TicketExitDto
    {
        public string TicketCode { get; set; } = string.Empty;
        public string ExitId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public DateTime ScanTime { get; set; } = DateTime.UtcNow;
    }

    // 刷卡出场 DTO
    public class CardExitDto
    {
        public string CardId { get; set; } = string.Empty;
        public string ExitId { get; set; } = string.Empty;
        public string? DetectedLicensePlate { get; set; }
        public DateTime SwipeTime { get; set; } = DateTime.UtcNow;
    }

    // 手动出场操作 DTO
    public class ManualExitActionDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public ExitAction Action { get; set; }
        public string? SelectedVehicleId { get; set; }
        public string? ModifiedLicensePlate { get; set; }
        public VehicleType? ModifiedVehicleType { get; set; }
        public DateTime? ManualEntryTime { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public DateTime ActionTime { get; set; } = DateTime.UtcNow;
    }

    // 出场处理结果 DTO
    public class ExitProcessResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public ParkingTransactionDto? Transaction { get; set; }
        public ExitDecisionDto? Decision { get; set; }
        public string? ReceiptData { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    }

    // 出口状态 DTO
    public class ExitStatusDto
    {
        public string ExitId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool IsOnline { get; set; }
        public bool IsBarrierOpen { get; set; }
        public bool IsCameraWorking { get; set; }
        public bool IsGroundSensorWorking { get; set; }
        public bool IsPrinterWorking { get; set; }
        public bool IsQrScannerWorking { get; set; }
        public int QueueLength { get; set; }
        public DateTime LastActivity { get; set; }
        public List<ExitVehicleDetectionDto> PendingVehicles { get; set; } = new();
    }

    // 班次统计 DTO
    public class ShiftSummaryDto
    {
        public string ShiftId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string OperatorName { get; set; } = string.Empty;
        public DateTime ShiftStart { get; set; }
        public DateTime? ShiftEnd { get; set; }
        public int TotalVehiclesProcessed { get; set; }
        public decimal TotalCashReceived { get; set; }
        public decimal TotalQrPayments { get; set; }
        public decimal TotalCouponValue { get; set; }
        public decimal TotalRevenue { get; set; }
        public int FreeExits { get; set; }
        public int ForceExits { get; set; }
        public List<PaymentBreakdownDto> PaymentBreakdown { get; set; } = new();
    }

    // 收费明细 DTO
    public class PaymentBreakdownDto
    {
        public PaymentMethod Method { get; set; }
        public int Count { get; set; }
        public decimal Amount { get; set; }
    }

    // 车位信息 DTO
    public class ParkingSpaceInfoDto
    {
        public int TotalSpaces { get; set; }
        public int OccupiedSpaces { get; set; }
        public int AvailableSpaces { get; set; }
        public double OccupancyRate { get; set; }
        public List<ZoneSpaceInfoDto> ZoneBreakdown { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }

    // 区域车位信息 DTO
    public class ZoneSpaceInfoDto
    {
        public string ZoneId { get; set; } = string.Empty;
        public string ZoneName { get; set; } = string.Empty;
        public int TotalSpaces { get; set; }
        public int OccupiedSpaces { get; set; }
        public int AvailableSpaces { get; set; }
    }

    // 岗亭控制 DTO
    public class BoothControlDto
    {
        public string BoothId { get; set; } = string.Empty;
        public List<string> ControlledExits { get; set; } = new();
        public List<string> ControlledEntrances { get; set; } = new();
        public string CurrentOperator { get; set; } = string.Empty;
        public DateTime LastActivity { get; set; }
        public bool IsActive { get; set; }
    }

    // 打印小票 DTO
    public class PrintReceiptDto
    {
        public string TransactionId { get; set; } = string.Empty;
        public string LicensePlate { get; set; } = string.Empty;
        public DateTime EntryTime { get; set; }
        public DateTime ExitTime { get; set; }
        public TimeSpan ParkingDuration { get; set; }
        public decimal TotalFee { get; set; }
        public decimal PaidAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public string OperatorName { get; set; } = string.Empty;
        public DateTime PrintTime { get; set; } = DateTime.UtcNow;
    }
}

namespace ParkingBoothApi.Models
{
    public enum ExitVehicleStatus
    {
        Detected,
        Processing,
        AwaitingPayment,
        PaymentCompleted,
        Exited,
        Rejected,
        ManualReview
    }

    public enum ExitAction
    {
        ProcessPayment,
        ForceExit,
        ManualMatch,
        ManualEntry,
        Recapture,
        Reject,
        FreeExit
    }

    public enum PaymentMethod
    {
        Cash,
        QrCode,
        Card,
        Coupon,
        Free,
        Mixed
    }

    public enum CouponType
    {
        CashCoupon,
        HourCoupon,
        FreeCoupon,
        DiscountCoupon
    }
}
