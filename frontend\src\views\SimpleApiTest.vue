<template>
  <div class="simple-test">
    <h2>简单API测试</h2>
    
    <div class="test-section">
      <h3>基础测试</h3>
      <button @click="testBasic" :disabled="loading">
        {{ loading ? '测试中...' : '测试基础功能' }}
      </button>
      <div v-if="result" class="result" :class="result.type">
        {{ result.message }}
      </div>
    </div>

    <div class="test-section">
      <h3>登录测试</h3>
      <div class="login-form">
        <input v-model="username" placeholder="用户名" />
        <input v-model="password" type="password" placeholder="密码" />
        <button @click="testLogin" :disabled="loginLoading">
          {{ loginLoading ? '登录中...' : '测试登录' }}
        </button>
      </div>
      <div v-if="loginResult" class="result" :class="loginResult.type">
        {{ loginResult.message }}
      </div>
    </div>

    <div class="logs">
      <h3>日志</h3>
      <div v-for="(log, index) in logs" :key="index" class="log-item">
        {{ log }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const loading = ref(false)
const loginLoading = ref(false)
const result = ref<{ type: string, message: string } | null>(null)
const loginResult = ref<{ type: string, message: string } | null>(null)
const logs = ref<string[]>([])
const username = ref('leo')
const password = ref('123456')

const addLog = (message: string) => {
  logs.value.unshift(`${new Date().toLocaleTimeString()}: ${message}`)
  if (logs.value.length > 10) {
    logs.value.pop()
  }
}

const testBasic = async () => {
  loading.value = true
  addLog('开始基础测试')
  
  try {
    // 简单的axios测试
    const axios = await import('axios')
    addLog('axios导入成功')
    
    result.value = {
      type: 'success',
      message: 'axios导入成功，基础功能正常'
    }
  } catch (error: any) {
    addLog(`基础测试失败: ${error.message}`)
    result.value = {
      type: 'error',
      message: `基础测试失败: ${error.message}`
    }
  } finally {
    loading.value = false
  }
}

const testLogin = async () => {
  loginLoading.value = true
  addLog('开始登录测试')
  
  try {
    // 动态导入API服务
    const { default: authApi } = await import('@/services/authApi')
    addLog('authApi导入成功')
    
    const response = await authApi.directLogin({
      username: username.value,
      password: password.value
    })
    
    addLog(`登录响应: ${JSON.stringify(response)}`)
    
    if (response.status === 1) {
      loginResult.value = {
        type: 'success',
        message: '登录测试成功'
      }
    } else {
      loginResult.value = {
        type: 'warning',
        message: `登录失败: ${response.msg || '未知错误'}`
      }
    }
  } catch (error: any) {
    addLog(`登录测试失败: ${error.message}`)
    loginResult.value = {
      type: 'error',
      message: `登录测试失败: ${error.message}`
    }
  } finally {
    loginLoading.value = false
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.login-form {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.login-form input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
}

.result.success {
  background: #d4edda;
  color: #155724;
}

.result.error {
  background: #f8d7da;
  color: #721c24;
}

.result.warning {
  background: #fff3cd;
  color: #856404;
}

.logs {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  margin-bottom: 5px;
  color: #666;
}
</style>
