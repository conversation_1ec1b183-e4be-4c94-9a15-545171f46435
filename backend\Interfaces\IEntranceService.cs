using ParkingBoothApi.DTOs;

namespace ParkingBoothApi.Interfaces
{
    public interface IEntranceService
    {
        /// <summary>
        /// 处理车辆检测
        /// </summary>
        Task<VehicleDetectionDto> ProcessVehicleDetectionAsync(VehicleDetectionDto detection);

        /// <summary>
        /// 进行入场决策
        /// </summary>
        Task<EntryDecisionDto> MakeEntryDecisionAsync(VehicleDetectionDto detection);

        /// <summary>
        /// 处理人工操作
        /// </summary>
        Task<EntryProcessResultDto> ProcessManualActionAsync(ManualEntryActionDto actionDto);

        /// <summary>
        /// 处理刷卡进场
        /// </summary>
        Task<EntryProcessResultDto> ProcessCardEntryAsync(CardEntryDto cardDto);

        /// <summary>
        /// 控制道闸
        /// </summary>
        Task<bool> ControlBarrierAsync(BarrierControlDto controlDto);

        /// <summary>
        /// 获取待处理通知
        /// </summary>
        Task<List<EntryNotificationDto>> GetPendingNotificationsAsync(string? entranceId = null);

        /// <summary>
        /// 获取入口状态
        /// </summary>
        Task<List<EntranceStatusDto>> GetEntranceStatusAsync();

        /// <summary>
        /// 获取入场统计
        /// </summary>
        Task<EntryStatisticsDto> GetEntryStatisticsAsync(DateTime date);
    }
}
