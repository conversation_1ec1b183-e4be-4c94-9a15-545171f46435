# Parking Booth System API Documentation

## Overview

The Parking Booth System API provides comprehensive endpoints for managing parking operations and hardware integration. This RESTful API is built with .NET 9 and follows OpenAPI specifications.

## Base URL

- Development: `http://localhost:5000/api`
- Production: `https://your-domain.com/api`

## Authentication

Currently, the API does not require authentication. In a production environment, consider implementing:
- API Key authentication
- JWT tokens
- OAuth 2.0

## Common Response Formats

### Success Response
```json
{
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2025-07-24T18:30:00Z"
}
```

### Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid license plate format",
    "details": { ... }
  },
  "timestamp": "2025-07-24T18:30:00Z"
}
```

## Parking Operations

### Record Vehicle Entry

**POST** `/parking/entry`

Records a new vehicle entering the parking facility.

**Request Body:**
```json
{
  "licensePlate": "ABC-123",
  "vehicleType": "car",
  "imageUrl": "http://example.com/image.jpg",
  "parkingSpot": "A1"
}
```

**Response:**
```json
{
  "id": "uuid",
  "licensePlate": "ABC-123",
  "entryTime": "2025-07-24T18:30:00Z",
  "vehicleType": "car",
  "imageUrl": "http://example.com/image.jpg",
  "parkingSpot": "A1",
  "status": "parked"
}
```

### Record Vehicle Exit

**POST** `/parking/exit`

Records a vehicle exiting the parking facility and creates a payment transaction.

**Request Body:**
```json
{
  "licensePlate": "ABC-123",
  "exitTime": "2025-07-24T20:30:00Z"
}
```

**Response:**
```json
{
  "id": "transaction-uuid",
  "vehicleId": "vehicle-uuid",
  "licensePlate": "ABC-123",
  "entryTime": "2025-07-24T18:30:00Z",
  "exitTime": "2025-07-24T20:30:00Z",
  "duration": 120,
  "amount": 4.00,
  "paymentStatus": "pending",
  "createdAt": "2025-07-24T20:30:00Z"
}
```

### Process Payment

**POST** `/parking/payment`

Processes payment for a parking transaction.

**Request Body:**
```json
{
  "transactionId": "transaction-uuid",
  "paymentMethod": "macau_pass",
  "paymentReference": "MP123456",
  "cardId": "MP789012",
  "cardBalance": 50.00
}
```

**Response:**
```json
{
  "success": true,
  "transactionId": "transaction-uuid",
  "paymentReference": "MP123456",
  "amountCharged": 4.00,
  "remainingBalance": 46.00,
  "processedAt": "2025-07-24T20:35:00Z"
}
```

## Hardware Control

### Get Hardware Status

**GET** `/hardware/status`

Returns the current status of all hardware devices.

**Response:**
```json
{
  "printer": {
    "connected": true,
    "status": "ready",
    "paperLevel": 85,
    "lastPrintTime": "2025-07-24T18:00:00Z"
  },
  "camera": {
    "connected": true,
    "status": "ready",
    "resolution": "1920x1080",
    "lastCapture": "2025-07-24T18:30:00Z"
  },
  "cardReader": {
    "connected": true,
    "status": "ready",
    "lastTransaction": "2025-07-24T18:25:00Z",
    "balance": 46.00
  },
  "groundSensor": {
    "connected": false,
    "status": "offline",
    "vehicleDetected": false,
    "lastDetection": null
  },
  "lastUpdated": "2025-07-24T18:35:00Z"
}
```

### Print Ticket

**POST** `/hardware/printer/print`

Prints a ticket or receipt.

**Request Body:**
```json
{
  "type": "receipt",
  "licensePlate": "ABC-123",
  "amount": 4.00,
  "paymentMethod": "macau_pass",
  "timestamp": "2025-07-24T20:35:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Ticket printed successfully",
  "data": {
    "printTime": "2025-07-24T20:35:00Z",
    "paperLevel": 84
  }
}
```

### Capture Image

**POST** `/hardware/camera/capture`

Captures an image from the camera.

**Request Body:**
```json
{
  "purpose": "entry",
  "vehicleId": "vehicle-uuid",
  "licensePlate": "ABC-123"
}
```

**Response:**
```json
{
  "success": true,
  "imageUrl": "/images/capture_20250724_203500.jpg",
  "imagePath": "/images/capture_20250724_203500.jpg",
  "timestamp": "2025-07-24T20:35:00Z",
  "message": "Image captured successfully"
}
```

### Read Card

**POST** `/hardware/cardreader/read`

Reads a Macau Pass card.

**Response:**
```json
{
  "success": true,
  "cardId": "MP789012",
  "balance": 50.00,
  "cardType": "Macau Pass",
  "readTime": "2025-07-24T20:35:00Z",
  "message": "Card read successfully"
}
```

### Check Vehicle Presence

**GET** `/hardware/groundsensor/detect`

Checks if a vehicle is present using the ground sensor.

**Response:**
```json
{
  "vehicleDetected": true,
  "checkTime": "2025-07-24T20:35:00Z",
  "sensorReading": 75,
  "message": "Vehicle detected"
}
```

## Statistics and Reports

### Get Today's Statistics

**GET** `/parking/stats/today`

Returns statistics for the current day.

**Response:**
```json
{
  "date": "2025-07-24",
  "totalVehicles": 127,
  "currentOccupancy": 45,
  "totalRevenue": 1250.50,
  "averageStayTimeMinutes": 85,
  "peakOccupancy": 67,
  "peakTime": "14:30:00",
  "vehicleTypeBreakdown": {
    "car": 95,
    "motorcycle": 25,
    "truck": 7
  },
  "paymentMethodBreakdown": {
    "cash": 450.25,
    "card": 320.75,
    "macau_pass": 479.50
  }
}
```

### Generate Report

**GET** `/parking/reports/{type}?date=2025-07-24`

Generates a report for the specified type and date.

**Parameters:**
- `type`: Report type (`daily`, `weekly`, `monthly`)
- `date`: Date for the report (ISO 8601 format)

**Response:**
```json
{
  "reportType": "daily",
  "date": "2025-07-24",
  "statistics": { ... },
  "generatedAt": "2025-07-24T20:35:00Z"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `VEHICLE_NOT_FOUND` | Vehicle not found in system |
| `VEHICLE_ALREADY_PARKED` | Vehicle is already parked |
| `CAPACITY_FULL` | Parking lot at full capacity |
| `PAYMENT_FAILED` | Payment processing failed |
| `HARDWARE_ERROR` | Hardware device error |
| `INSUFFICIENT_FUNDS` | Card has insufficient funds |
| `DEVICE_OFFLINE` | Hardware device is offline |

## Rate Limiting

The API implements rate limiting to prevent abuse:
- 100 requests per minute per IP address
- 1000 requests per hour per IP address

## Webhooks

The system can send webhooks for important events:
- Vehicle entry/exit
- Payment completion
- Hardware failures
- Capacity alerts

## SDK and Libraries

Official SDKs are available for:
- JavaScript/TypeScript
- Python
- C#
- Java

## Support

For API support, please contact:
- Email: <EMAIL>
- Documentation: https://docs.parkingbooth.com
- Status Page: https://status.parkingbooth.com
