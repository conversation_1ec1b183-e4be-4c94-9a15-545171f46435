import httpClient from './httpClient'
import type { ApiResponse } from './httpClient'

// 首页统计数据
export interface HomeStats {
  totalAmount: number
  totalVehicles: number
  currentOccupancy: number
  availableSpaces: number
}

// 车辆进出记录
export interface VehicleRecord {
  id: string
  plateNumber: string
  entryTime: string
  exitTime?: string
  vehicleType: string
  paymentStatus: string
  amount?: number
  entryChannel: string
  exitChannel?: string
  operator: string
}

// 收费明细
export interface ChargeDetail {
  id: string
  plateNumber: string
  entryTime: string
  exitTime: string
  parkingDuration: number
  amount: number
  paymentMethod: string
  paymentTime: string
  operator: string
  transactionId: string
}

// 场内车辆
export interface VehicleInPark {
  id: string
  plateNumber: string
  entryTime: string
  vehicleType: string
  parkingDuration: number
  entryChannel: string
  operator: string
  status: string
}

// 停车位组
export interface ParkingGroup {
  id: string
  name: string
  totalSpaces: number
  occupiedSpaces: number
  availableSpaces: number
  description?: string
}

// 通道信息
export interface Channel {
  id: string
  name: string
  type: 'entry' | 'exit' | 'both'
  status: 'online' | 'offline'
  cameraIp: string
  mac: string
  description?: string
}

class ParkingApi {
  // 首页统计数据
  async getHomeStats(): Promise<ApiResponse<HomeStats>> {
    try {
      const response = await httpClient.post('/home/<USER>/sumInfoReport')
      return response
    } catch (error) {
      console.error('获取首页统计失败:', error)
      throw error
    }
  }

  // 今日收费统计（饼图）
  async getStatisticsReport(): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/home/<USER>/statisticsReport')
      return response
    } catch (error) {
      console.error('获取收费统计失败:', error)
      throw error
    }
  }

  // 车辆进出场数量报表
  async getCarNumReport(): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/home/<USER>/CarNumReport')
      return response
    } catch (error) {
      console.error('获取车辆数量报表失败:', error)
      throw error
    }
  }

  // 设备在线状态
  async getChannelStatus(): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/home/<USER>/channelStatusReport')
      return response
    } catch (error) {
      console.error('获取设备状态失败:', error)
      throw error
    }
  }

  // 获取出入明细
  async getVehicleRecords(params: {
    startDate?: string
    endDate?: string
    plateNumber?: string
    vehicleType?: string
    pageNum?: number
    pageSize?: number
  }): Promise<ApiResponse<{ list: VehicleRecord[], total: number }>> {
    try {
      const response = await httpClient.post('/report/getCarNumInfo', params)
      return response
    } catch (error) {
      console.error('获取出入明细失败:', error)
      throw error
    }
  }

  // 获取收费明细
  async getChargeDetails(params: {
    startDate?: string
    endDate?: string
    plateNumber?: string
    paymentMethod?: string
    pageNum?: number
    pageSize?: number
  }): Promise<ApiResponse<{ list: ChargeDetail[], total: number }>> {
    try {
      const response = await httpClient.post('/report/getChargeInfo', params)
      return response
    } catch (error) {
      console.error('获取收费明细失败:', error)
      throw error
    }
  }

  // 获取场内车辆
  async getVehiclesInPark(params: {
    plateNumber?: string
    vehicleType?: string
    pageNum?: number
    pageSize?: number
  }): Promise<ApiResponse<{ list: VehicleInPark[], total: number }>> {
    try {
      const response = await httpClient.post('/report/getCarInInfo', params)
      return response
    } catch (error) {
      console.error('获取场内车辆失败:', error)
      throw error
    }
  }

  // 获取停车位组
  async getParkingGroups(): Promise<ApiResponse<ParkingGroup[]>> {
    try {
      const response = await httpClient.post('/group/findAll')
      return response
    } catch (error) {
      console.error('获取停车位组失败:', error)
      throw error
    }
  }

  // 获取通道列表
  async getChannels(): Promise<ApiResponse<Channel[]>> {
    try {
      const response = await httpClient.post('/channel/findAllInfo')
      return response
    } catch (error) {
      console.error('获取通道列表失败:', error)
      throw error
    }
  }

  // 开闸/关闸/补拍
  async controlGate(channelMac: string, action: 0 | 1 | 3): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.get(`/callCenter/openOrReshoot?channnelMac=${channelMac}&action=${action}`)
      return response
    } catch (error) {
      console.error('控制闸机失败:', error)
      throw error
    }
  }

  // 车牌修改
  async updatePlateNumber(data: {
    carNumUid: string
    newPlateNumber: string
    reason?: string
  }): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/callCenter/updateCaNo', data)
      return response
    } catch (error) {
      console.error('修改车牌失败:', error)
      throw error
    }
  }

  // 免费放行
  async freePass(data: {
    plateNumber: string
    reason: string
    operator: string
  }): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/callCenter/confirm', {
        ...data,
        type: 'free'
      })
      return response
    } catch (error) {
      console.error('免费放行失败:', error)
      throw error
    }
  }

  // 相似车牌查询
  async findSimilarPlates(plateNumber: string): Promise<ApiResponse<string[]>> {
    try {
      const response = await httpClient.post('/callCenter/findsimilarCar', {
        plateNumber: plateNumber
      })
      return response
    } catch (error) {
      console.error('查询相似车牌失败:', error)
      throw error
    }
  }

  // 重新计费
  async recharge(data: {
    carNumUid: string
    vehicleType: string
  }): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/callCenter/chargeAgain', data)
      return response
    } catch (error) {
      console.error('重新计费失败:', error)
      throw error
    }
  }

  // 获取车辆照片
  async getVehiclePhoto(carNumUid: string): Promise<string> {
    try {
      const baseUrl = httpClient.getBaseURL()
      return `${baseUrl}/getPicture/find?carNumUid=${carNumUid}`
    } catch (error) {
      console.error('获取车辆照片失败:', error)
      throw error
    }
  }

  // 导出出入明细
  async exportVehicleRecords(params: any): Promise<ArrayBuffer> {
    try {
      const response = await httpClient.download('/report/getAllCarNumInfo', params)
      return response
    } catch (error) {
      console.error('导出出入明细失败:', error)
      throw error
    }
  }

  // 导出收费明细
  async exportChargeDetails(params: any): Promise<ArrayBuffer> {
    try {
      const response = await httpClient.download('/report/getAllChargeInfo', params)
      return response
    } catch (error) {
      console.error('导出收费明细失败:', error)
      throw error
    }
  }

  // 获取交班记录
  async getShiftRecords(params: {
    startDate?: string
    endDate?: string
    operator?: string
    pageNum?: number
    pageSize?: number
  }): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/report/getShiftRecord', params)
      return response
    } catch (error) {
      console.error('获取交班记录失败:', error)
      throw error
    }
  }

  // 获取系统参数
  async getSystemParams(): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/paramster/getCarParams')
      return response
    } catch (error) {
      console.error('获取系统参数失败:', error)
      throw error
    }
  }

  // 保存系统参数
  async saveSystemParams(params: any): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/paramster/batchUpdate', params)
      return response
    } catch (error) {
      console.error('保存系统参数失败:', error)
      throw error
    }
  }
}

// 创建单例实例
export const parkingApi = new ParkingApi()
export default parkingApi
