<template>
  <div class="notification-test-page">
    <div class="test-container">
      <h1>美化通知系统测试</h1>
      
      <div class="test-section">
        <h2>基础通知测试</h2>
        <div class="button-group">
          <button @click="testSuccess" class="test-btn success">
            ✅ 成功通知
          </button>
          <button @click="testError" class="test-btn error">
            ❌ 错误通知
          </button>
          <button @click="testWarning" class="test-btn warning">
            ⚠️ 警告通知
          </button>
          <button @click="testInfo" class="test-btn info">
            ℹ️ 信息通知
          </button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>确认对话框测试</h2>
        <div class="button-group">
          <button @click="testConfirm" class="test-btn confirm">
            🤔 确认对话框
          </button>
          <button @click="testLogout" class="test-btn logout">
            🚪 注销确认
          </button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>停车场通知测试</h2>
        <div class="button-group">
          <button @click="testParkingSuccess" class="test-btn parking">
            🅿️ 车位修改成功
          </button>
          <button @click="testParkingError" class="test-btn parking">
            🚫 车位修改错误
          </button>
        </div>
      </div>
      
      <div class="test-section">
        <h2>批量通知测试</h2>
        <div class="button-group">
          <button @click="testMultiple" class="test-btn multiple">
            📢 批量通知
          </button>
          <button @click="clearAll" class="test-btn clear">
            🧹 清除所有
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { notification } from '../utils/notification'

const testSuccess = () => {
  notification.success('操作成功完成！', '成功')
}

const testError = () => {
  notification.error('操作失败，请检查输入信息', '错误')
}

const testWarning = () => {
  notification.warning('请注意：此操作不可撤销', '警告')
}

const testInfo = () => {
  notification.info('系统将在5分钟后进行维护', '系统通知')
}

const testConfirm = async () => {
  const result = await notification.confirm({
    title: '确认操作',
    message: '您确定要执行此操作吗？此操作不可撤销。',
    type: 'warning'
  })
  
  if (result) {
    notification.success('您选择了确认', '操作结果')
  } else {
    notification.info('您取消了操作', '操作结果')
  }
}

const testLogout = async () => {
  const result = await notification.confirm({
    title: '确认注销',
    message: '您确定要注销当前账户吗？注销后需要重新登录才能继续使用系统',
    type: 'error',
    confirmText: '确认注销',
    cancelText: '取消'
  })
  
  if (result) {
    notification.success('注销成功', '系统提示')
  } else {
    notification.info('已取消注销', '系统提示')
  }
}

const testParkingSuccess = () => {
  notification.success('私家车区域剩余车位已修改为: 250', '修改成功')
}

const testParkingError = () => {
  notification.warning('剩余车位数不能大于总车位数', '输入错误')
}

const testMultiple = () => {
  notification.info('开始批量操作...', '系统提示')
  
  setTimeout(() => {
    notification.success('第1步完成', '进度更新')
  }, 1000)
  
  setTimeout(() => {
    notification.success('第2步完成', '进度更新')
  }, 2000)
  
  setTimeout(() => {
    notification.warning('第3步遇到警告', '进度更新')
  }, 3000)
  
  setTimeout(() => {
    notification.success('所有操作完成！', '批量操作')
  }, 4000)
}

const clearAll = () => {
  notification.clearAll()
  notification.info('已清除所有通知', '系统提示')
}
</script>

<style scoped>
/* 停车收费系统测试页面样式 */
.notification-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-container {
  max-width: 900px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 40px;
  font-size: 28px;
  font-weight: 600;
}

.test-section {
  margin-bottom: 40px;
}

h2 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #e1e5e9;
  padding-bottom: 10px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.test-btn {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  background: #ffffff;
}

.test-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background: currentColor;
  color: #ffffff !important;
}

/* 停车系统专业配色 */
.test-btn.success {
  border-color: #28a745;
  color: #28a745;
}

.test-btn.error {
  border-color: #dc3545;
  color: #dc3545;
}

.test-btn.warning {
  border-color: #ffc107;
  color: #856404;
}

.test-btn.info {
  border-color: #4a90e2;
  color: #4a90e2;
}

.test-btn.confirm {
  border-color: #6c757d;
  color: #6c757d;
}

.test-btn.logout {
  border-color: #dc3545;
  color: #dc3545;
}

.test-btn.parking {
  border-color: #28a745;
  color: #28a745;
}

.test-btn.multiple {
  border-color: #4a90e2;
  color: #4a90e2;
}

.test-btn.clear {
  border-color: #6c757d;
  color: #6c757d;
}

@media (max-width: 768px) {
  .notification-test-page {
    padding: 20px 10px;
  }

  .test-container {
    padding: 20px;
  }

  h1 {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .test-section {
    margin-bottom: 30px;
  }

  .button-group {
    flex-direction: column;
  }

  .test-btn {
    width: 100%;
    min-width: auto;
  }
}

@media (min-width: 1200px) {
  .test-container {
    max-width: 1000px;
  }
}
</style>
