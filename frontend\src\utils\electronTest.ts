// Electron API 测试工具
// 用于验证桌面应用功能是否正常

export class ElectronTester {
  // 检查是否在 Electron 环境中运行
  static isElectron(): boolean {
    return !!(window as any).electronAPI
  }

  // 测试基础 API
  static async testBasicAPI() {
    if (!this.isElectron()) {
      console.log('❌ 不在 Electron 环境中运行')
      return false
    }

    try {
      // 测试获取应用版本
      const version = await (window as any).electronAPI.getAppVersion()
      console.log('✅ 应用版本:', version)

      // 测试平台信息
      const platform = (window as any).electronAPI.platform
      console.log('✅ 运行平台:', platform)

      return true
    } catch (error) {
      console.error('❌ 基础 API 测试失败:', error)
      return false
    }
  }

  // 测试对话框 API
  static async testDialogAPI() {
    if (!this.isElectron()) return false

    try {
      // 测试消息框
      await (window as any).electronAPI.showMessageBox({
        type: 'info',
        title: 'Electron 测试',
        message: '恭喜！Electron 桌面应用运行正常！\n\n✅ 基础功能已就绪\n✅ API 接口正常\n✅ 可以开始使用停车系统功能',
        detail: '这是一个测试消息，验证 Electron 对话框功能是否正常工作。'
      })

      console.log('✅ 对话框 API 测试成功')
      return true
    } catch (error) {
      console.error('❌ 对话框 API 测试失败:', error)
      return false
    }
  }

  // 测试硬件 API（模拟）
  static async testHardwareAPI() {
    if (!this.isElectron()) return false

    try {
      console.log('🔧 硬件 API 接口检查:')
      
      const api = (window as any).electronAPI.hardware
      
      // 检查各个硬件接口是否存在
      const interfaces = [
        'printer',
        'camera', 
        'cardReader',
        'groundSensor',
        'barrier'
      ]

      interfaces.forEach(interfaceName => {
        if (api[interfaceName]) {
          console.log(`✅ ${interfaceName} 接口已就绪`)
        } else {
          console.log(`❌ ${interfaceName} 接口缺失`)
        }
      })

      return true
    } catch (error) {
      console.error('❌ 硬件 API 测试失败:', error)
      return false
    }
  }

  // 运行完整测试
  static async runFullTest() {
    console.log('🚀 开始 Electron 功能测试...')
    
    const results = {
      basic: await this.testBasicAPI(),
      dialog: await this.testDialogAPI(),
      hardware: await this.testHardwareAPI()
    }

    console.log('📊 测试结果:', results)
    
    const allPassed = Object.values(results).every(result => result)
    
    if (allPassed) {
      console.log('🎉 所有测试通过！Electron 桌面应用已准备就绪！')
    } else {
      console.log('⚠️ 部分测试失败，请检查配置')
    }

    return results
  }
}

// 自动检测并显示环境信息
if (typeof window !== 'undefined') {
  console.log('🔍 环境检测:')
  console.log('- 是否为 Electron 环境:', ElectronTester.isElectron())
  console.log('- User Agent:', navigator.userAgent)
  
  // 如果在 Electron 环境中，显示可用的 API
  if (ElectronTester.isElectron()) {
    console.log('- 可用的 Electron API:', Object.keys((window as any).electronAPI))
  }
}
