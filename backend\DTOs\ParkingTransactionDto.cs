using System.ComponentModel.DataAnnotations;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.DTOs
{
    public class ParkingTransactionDto
    {
        public string Id { get; set; } = string.Empty;
        public string VehicleId { get; set; } = string.Empty;
        public string LicensePlate { get; set; } = string.Empty;
        public DateTime EntryTime { get; set; }
        public DateTime? ExitTime { get; set; }
        public int? Duration { get; set; }
        public decimal Amount { get; set; }
        public PaymentMethod? PaymentMethod { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
        public string? PaymentReference { get; set; }
        public DateTime CreatedAt { get; set; }
    }
    
    public class CreateTransactionDto
    {
        [Required]
        public string VehicleId { get; set; } = string.Empty;
        
        [Required]
        public DateTime ExitTime { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
    }
    
    public class ProcessPaymentDto
    {
        [Required]
        public string TransactionId { get; set; } = string.Empty;
        
        [Required]
        public PaymentMethod PaymentMethod { get; set; }
        
        [StringLength(100)]
        public string? PaymentReference { get; set; }
        
        public decimal? AmountPaid { get; set; }
        
        // For card payments
        public string? CardId { get; set; }
        public decimal? CardBalance { get; set; }
    }
    
    public class PaymentResponseDto
    {
        public bool Success { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string? PaymentReference { get; set; }
        public decimal AmountCharged { get; set; }
        public decimal? RemainingBalance { get; set; }
        public string? Message { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    }
    
    public class FeeCalculationDto
    {
        public string VehicleId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int DurationMinutes { get; set; }
        public FeeBreakdown Breakdown { get; set; } = new();
    }
    
    public class FeeBreakdown
    {
        public decimal BaseRate { get; set; }
        public int Hours { get; set; }
        public decimal HourlyCharge { get; set; }
        public decimal MinimumFee { get; set; }
        public decimal Multiplier { get; set; } = 1.0m;
        public string? MultiplierReason { get; set; }
        public decimal SubTotal { get; set; }
        public decimal Tax { get; set; } = 0;
        public decimal Total { get; set; }
        public bool IsGracePeriod { get; set; }
    }
    
    public class TransactionSearchDto
    {
        public string? LicensePlate { get; set; }
        public PaymentStatus? PaymentStatus { get; set; }
        public PaymentMethod? PaymentMethod { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public decimal? AmountFrom { get; set; }
        public decimal? AmountTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
    
    public class TransactionListResponseDto
    {
        public List<ParkingTransactionDto> Transactions { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public decimal TotalAmount { get; set; }
    }
}
