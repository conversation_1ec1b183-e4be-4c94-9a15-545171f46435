import httpClient from './httpClient'
import type { ApiResponse, LoginResponse, UserInfo } from './httpClient'

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
}

// 修改密码请求参数
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
}

// 用户权限信息
export interface UserPermissions {
  roleId: string
  roleName: string
  permissions: string[]
  menus: any[]
}

class AuthApi {
  // 登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // 使用OAuth2登录方式
      const response = await httpClient.postForm('/oauth/token', {
        grant_type: 'password',
        client_id: 'yard',
        client_secret: 'yard-8888',
        username: credentials.username,
        password: credentials.password
      })

      return response as LoginResponse
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 直接登录（使用auth/login接口）
  async directLogin(credentials: LoginRequest): Promise<ApiResponse<any>> {
    try {
      // 使用GET请求，参数通过URL查询字符串传递
      const response = await httpClient.postForm(`/auth/login?userName=${credentials.username}&password=${credentials.password}`, {
        // userName: credentials.username,
        // password: credentials.password
      })

      return response
    } catch (error) {
      console.error('直接登录失败:', error)
      throw error
    }
  }

  // 退出登录
  async logout(): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/auth/logout')
      return response
    } catch (error) {
      console.error('退出登录失败:', error)
      throw error
    }
  }

  // 获取用户信息
  async getUserInfo(username?: string): Promise<ApiResponse<UserInfo>> {
    try {
      const response = await httpClient.post('/user/findUserByName', {
        username: username
      })

      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 修改密码
  async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/user/updatePwd', {
        oldPassword: data.oldPassword,
        newPassword: data.newPassword
      })

      return response
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }

  // 重置密码
  async resetPassword(userId: string): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/user/resetUserPwd', {
        userId: userId
      })

      return response
    } catch (error) {
      console.error('重置密码失败:', error)
      throw error
    }
  }

  // 验证用户名唯一性
  async validateUsername(username: string): Promise<ApiResponse<boolean>> {
    try {
      const response = await httpClient.post('/user/valiaccount', {
        username: username
      })

      return response
    } catch (error) {
      console.error('验证用户名失败:', error)
      throw error
    }
  }

  // 获取用户权限
  async getUserPermissions(roleId: string): Promise<ApiResponse<UserPermissions>> {
    try {
      const response = await httpClient.post('/UserPermissions/getRoleMenuView', {
        roleId: roleId
      })

      return response
    } catch (error) {
      console.error('获取用户权限失败:', error)
      throw error
    }
  }

  // 获取车场授权参数
  async getParkParams(): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/parkAuth/getParkParams')
      return response
    } catch (error) {
      console.error('获取车场授权参数失败:', error)
      throw error
    }
  }

  // 车场授权
  async parkingAuth(authData: any): Promise<ApiResponse<any>> {
    try {
      const response = await httpClient.post('/parkAuth/parkingAuth', authData)
      return response
    } catch (error) {
      console.error('车场授权失败:', error)
      throw error
    }
  }
}

// 创建单例实例
export const authApi = new AuthApi()
export default authApi
