# HONGRUI 停车收费系统 - Electron桌面客户端

## 概述

本项目已集成Electron支持，可以将Vue.js前端应用打包为桌面客户端应用。

## 功能特性

### 🖥️ 桌面应用特性
- **原生窗口控制**：最小化、最大化、关闭
- **菜单栏**：完整的应用菜单系统
- **文件对话框**：打开、保存文件对话框
- **系统托盘**：后台运行支持
- **自动更新**：支持应用自动更新

### 🔧 停车系统专用功能
- **硬件接口**：打印机、摄像头、读卡器、地感传感器
- **数据库操作**：本地数据存储和查询
- **配置管理**：系统配置持久化
- **日志记录**：完整的操作日志
- **网络代理**：安全的HTTP请求处理

## 安装和运行

### 1. 安装依赖

```bash
# 安装Electron相关依赖
npm install --save-dev electron electron-builder concurrently wait-on

# 如果安装失败，可以尝试单独安装
npm install --save-dev concurrently wait-on
```

### 2. 开发模式运行

```bash
# 方式1：同时启动Vite开发服务器和Electron
npm run electron-dev

# 方式2：分别启动
# 终端1：启动Vite开发服务器
npm run dev

# 终端2：启动Electron（等Vite启动后）
npm run electron
```

### 3. 构建生产版本

```bash
# 构建Vue应用
npm run build

# 打包Electron应用
npm run electron-pack

# 构建分发版本
npm run electron-dist
```

## 项目结构

```
frontend/
├── electron/                 # Electron主进程文件
│   ├── main.js               # 主进程入口
│   ├── preload.js            # 预加载脚本
│   └── assets/               # 应用图标等资源
├── src/                      # Vue.js源代码
├── dist/                     # 构建输出
├── dist-electron/            # Electron打包输出
└── package.json              # 项目配置
```

## 配置说明

### package.json配置

```json
{
  "main": "electron/main.js",
  "scripts": {
    "electron": "electron .",
    "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"",
    "electron-pack": "npm run build && electron-builder",
    "electron-dist": "npm run build && electron-builder --publish=never"
  },
  "build": {
    "appId": "com.HONGRUI.parking-system",
    "productName": "HONGRUI停车收费系统",
    "directories": {
      "output": "dist-electron"
    }
  }
}
```

### 窗口配置

- **默认尺寸**：1400x900
- **最小尺寸**：1200x800
- **安全设置**：禁用node集成，启用上下文隔离
- **预加载脚本**：安全的API暴露

## API接口

### 系统API

```javascript
// 获取应用版本
const version = await window.electronAPI.getAppVersion()

// 显示消息框
await window.electronAPI.showMessageBox({
  type: 'info',
  title: '提示',
  message: '操作成功'
})
```

### 硬件控制API

```javascript
// 打印机控制
await window.electronAPI.hardware.printer.print(data)
const printerStatus = await window.electronAPI.hardware.printer.getStatus()

// 摄像头控制
await window.electronAPI.hardware.camera.capture()
await window.electronAPI.hardware.camera.startStream()

// 读卡器控制
const cardData = await window.electronAPI.hardware.cardReader.read()

// 道闸控制
await window.electronAPI.hardware.barrier.open()
await window.electronAPI.hardware.barrier.close()
```

### 数据库API

```javascript
// 数据库查询
const results = await window.electronAPI.database.query('SELECT * FROM vehicles')

// 插入数据
await window.electronAPI.database.insert('vehicles', {
  license_plate: 'ABC123',
  entry_time: new Date()
})
```

## 打包分发

### Windows

```bash
npm run electron-dist
```

生成文件：
- `dist-electron/HONGRUI停车收费系统 Setup 1.0.0.exe` - 安装程序
- `dist-electron/win-unpacked/` - 免安装版本

### macOS

```bash
npm run electron-dist
```

生成文件：
- `dist-electron/HONGRUI停车收费系统-1.0.0.dmg` - 磁盘映像
- `dist-electron/mac/` - 应用包

### Linux

```bash
npm run electron-dist
```

生成文件：
- `dist-electron/HONGRUI停车收费系统-1.0.0.AppImage` - AppImage格式

## 开发注意事项

### 1. 安全性
- 禁用了node集成
- 启用了上下文隔离
- 使用preload脚本安全暴露API

### 2. 硬件集成
- 所有硬件操作都通过主进程处理
- 使用IPC通信确保安全性
- 支持异步操作和事件监听

### 3. 数据存储
- 使用SQLite本地数据库
- 配置文件存储在用户目录
- 支持数据备份和恢复

## 故障排除

### 1. Electron安装失败
```bash
# 清除缓存
npm cache clean --force

# 使用淘宝镜像
npm config set registry https://registry.npmmirror.com/
npm config set electron_mirror https://npmmirror.com/mirrors/electron/

# 重新安装
npm install electron
```

### 2. 构建失败
```bash
# 检查Node.js版本（推荐16+）
node --version

# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
```

### 3. 硬件设备无法访问
- 确保以管理员权限运行
- 检查设备驱动程序
- 查看控制台错误信息

## 技术支持

如有问题，请检查：
1. Node.js版本是否兼容
2. 依赖是否正确安装
3. 硬件设备是否正常连接
4. 防火墙和杀毒软件设置

更多信息请参考：
- [Electron官方文档](https://www.electronjs.org/docs)
- [Vue.js官方文档](https://vuejs.org/)
- [项目GitHub仓库](https://github.com/your-repo)
