using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Services
{
    public class ParkingService : IParkingService
    {
        private readonly ILogger<ParkingService> _logger;
        private static readonly List<Vehicle> _vehicles = new();
        private static readonly List<ParkingTransaction> _transactions = new();
        private static ParkingConfiguration _configuration = new();

        public ParkingService(ILogger<ParkingService> logger)
        {
            _logger = logger;
        }

        public async Task<VehicleDto> RecordVehicleEntryAsync(CreateVehicleEntryDto entryDto)
        {
            _logger.LogInformation("Recording entry for vehicle: {LicensePlate}", entryDto.LicensePlate);

            var vehicle = new Vehicle
            {
                Id = Guid.NewGuid().ToString(),
                LicensePlate = entryDto.LicensePlate.ToUpper(),
                EntryTime = DateTime.UtcNow,
                VehicleType = entryDto.VehicleType,
                ImageUrl = entryDto.ImageUrl,
                ParkingSpot = entryDto.ParkingSpot,
                Status = VehicleStatus.Parked
            };

            _vehicles.Add(vehicle);

            return await Task.FromResult(new VehicleDto
            {
                Id = vehicle.Id,
                LicensePlate = vehicle.LicensePlate,
                EntryTime = vehicle.EntryTime,
                ExitTime = vehicle.ExitTime,
                ParkingSpot = vehicle.ParkingSpot,
                VehicleType = vehicle.VehicleType,
                ImageUrl = vehicle.ImageUrl,
                Status = vehicle.Status
            });
        }

        public async Task<ParkingTransactionDto> RecordVehicleExitAsync(VehicleExitDto exitDto)
        {
            _logger.LogInformation("Recording exit for vehicle: {LicensePlate}", exitDto.LicensePlate);

            var vehicle = _vehicles.FirstOrDefault(v => 
                v.LicensePlate == exitDto.LicensePlate.ToUpper() && 
                v.Status == VehicleStatus.Parked);

            if (vehicle == null)
            {
                throw new ArgumentException($"Vehicle with license plate {exitDto.LicensePlate} not found or already exited");
            }

            var exitTime = exitDto.ExitTime ?? DateTime.UtcNow;
            var duration = (int)(exitTime - vehicle.EntryTime).TotalMinutes;
            var amount = CalculateFee(duration, vehicle.VehicleType);

            vehicle.ExitTime = exitTime;
            vehicle.Status = VehicleStatus.PaymentPending;

            var transaction = new ParkingTransaction
            {
                Id = Guid.NewGuid().ToString(),
                VehicleId = vehicle.Id,
                LicensePlate = vehicle.LicensePlate,
                EntryTime = vehicle.EntryTime,
                ExitTime = exitTime,
                Duration = duration,
                Amount = amount,
                PaymentStatus = PaymentStatus.Pending
            };

            _transactions.Add(transaction);

            return await Task.FromResult(new ParkingTransactionDto
            {
                Id = transaction.Id,
                VehicleId = transaction.VehicleId,
                LicensePlate = transaction.LicensePlate,
                EntryTime = transaction.EntryTime,
                ExitTime = transaction.ExitTime,
                Duration = transaction.Duration,
                Amount = transaction.Amount,
                PaymentMethod = transaction.PaymentMethod,
                PaymentStatus = transaction.PaymentStatus,
                PaymentReference = transaction.PaymentReference,
                CreatedAt = transaction.CreatedAt
            });
        }

        public async Task<VehicleDto?> GetVehicleByIdAsync(string vehicleId)
        {
            var vehicle = _vehicles.FirstOrDefault(v => v.Id == vehicleId);
            if (vehicle == null) return null;

            return await Task.FromResult(new VehicleDto
            {
                Id = vehicle.Id,
                LicensePlate = vehicle.LicensePlate,
                EntryTime = vehicle.EntryTime,
                ExitTime = vehicle.ExitTime,
                ParkingSpot = vehicle.ParkingSpot,
                VehicleType = vehicle.VehicleType,
                ImageUrl = vehicle.ImageUrl,
                Status = vehicle.Status
            });
        }

        public async Task<VehicleDto?> GetVehicleByLicensePlateAsync(string licensePlate)
        {
            var vehicle = _vehicles.FirstOrDefault(v => 
                v.LicensePlate == licensePlate.ToUpper() && 
                v.Status == VehicleStatus.Parked);
            
            if (vehicle == null) return null;

            return await Task.FromResult(new VehicleDto
            {
                Id = vehicle.Id,
                LicensePlate = vehicle.LicensePlate,
                EntryTime = vehicle.EntryTime,
                ExitTime = vehicle.ExitTime,
                ParkingSpot = vehicle.ParkingSpot,
                VehicleType = vehicle.VehicleType,
                ImageUrl = vehicle.ImageUrl,
                Status = vehicle.Status
            });
        }

        public async Task<VehicleListResponseDto> GetCurrentVehiclesAsync(VehicleSearchDto searchDto)
        {
            var query = _vehicles.Where(v => v.Status == VehicleStatus.Parked);

            if (!string.IsNullOrEmpty(searchDto.LicensePlate))
            {
                query = query.Where(v => v.LicensePlate.Contains(searchDto.LicensePlate.ToUpper()));
            }

            if (searchDto.VehicleType.HasValue)
            {
                query = query.Where(v => v.VehicleType == searchDto.VehicleType.Value);
            }

            var totalCount = query.Count();
            var vehicles = query
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .Select(v => new VehicleDto
                {
                    Id = v.Id,
                    LicensePlate = v.LicensePlate,
                    EntryTime = v.EntryTime,
                    ExitTime = v.ExitTime,
                    ParkingSpot = v.ParkingSpot,
                    VehicleType = v.VehicleType,
                    ImageUrl = v.ImageUrl,
                    Status = v.Status
                })
                .ToList();

            return await Task.FromResult(new VehicleListResponseDto
            {
                Vehicles = vehicles,
                TotalCount = totalCount,
                Page = searchDto.Page,
                PageSize = searchDto.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
            });
        }

        public async Task<VehicleListResponseDto> SearchVehiclesAsync(VehicleSearchDto searchDto)
        {
            return await GetCurrentVehiclesAsync(searchDto);
        }

        public async Task<ParkingTransactionDto?> GetTransactionByIdAsync(string transactionId)
        {
            var transaction = _transactions.FirstOrDefault(t => t.Id == transactionId);
            if (transaction == null) return null;

            return await Task.FromResult(new ParkingTransactionDto
            {
                Id = transaction.Id,
                VehicleId = transaction.VehicleId,
                LicensePlate = transaction.LicensePlate,
                EntryTime = transaction.EntryTime,
                ExitTime = transaction.ExitTime,
                Duration = transaction.Duration,
                Amount = transaction.Amount,
                PaymentMethod = transaction.PaymentMethod,
                PaymentStatus = transaction.PaymentStatus,
                PaymentReference = transaction.PaymentReference,
                CreatedAt = transaction.CreatedAt
            });
        }

        public async Task<TransactionListResponseDto> GetTransactionsAsync(TransactionSearchDto searchDto)
        {
            var query = _transactions.AsQueryable();

            if (!string.IsNullOrEmpty(searchDto.LicensePlate))
            {
                query = query.Where(t => t.LicensePlate.Contains(searchDto.LicensePlate.ToUpper()));
            }

            if (searchDto.PaymentStatus.HasValue)
            {
                query = query.Where(t => t.PaymentStatus == searchDto.PaymentStatus.Value);
            }

            if (searchDto.DateFrom.HasValue)
            {
                query = query.Where(t => t.CreatedAt >= searchDto.DateFrom.Value);
            }

            if (searchDto.DateTo.HasValue)
            {
                query = query.Where(t => t.CreatedAt <= searchDto.DateTo.Value);
            }

            var totalCount = query.Count();
            var totalAmount = query.Sum(t => t.Amount);
            
            var transactions = query
                .OrderByDescending(t => t.CreatedAt)
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .Select(t => new ParkingTransactionDto
                {
                    Id = t.Id,
                    VehicleId = t.VehicleId,
                    LicensePlate = t.LicensePlate,
                    EntryTime = t.EntryTime,
                    ExitTime = t.ExitTime,
                    Duration = t.Duration,
                    Amount = t.Amount,
                    PaymentMethod = t.PaymentMethod,
                    PaymentStatus = t.PaymentStatus,
                    PaymentReference = t.PaymentReference,
                    CreatedAt = t.CreatedAt
                })
                .ToList();

            return await Task.FromResult(new TransactionListResponseDto
            {
                Transactions = transactions,
                TotalCount = totalCount,
                Page = searchDto.Page,
                PageSize = searchDto.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize),
                TotalAmount = totalAmount
            });
        }

        public async Task<TransactionListResponseDto> GetPendingTransactionsAsync()
        {
            var searchDto = new TransactionSearchDto
            {
                PaymentStatus = PaymentStatus.Pending,
                PageSize = 100
            };
            return await GetTransactionsAsync(searchDto);
        }

        public async Task<PaymentResponseDto> ProcessPaymentAsync(ProcessPaymentDto paymentDto)
        {
            var transaction = _transactions.FirstOrDefault(t => t.Id == paymentDto.TransactionId);
            if (transaction == null)
            {
                throw new ArgumentException($"Transaction {paymentDto.TransactionId} not found");
            }

            if (transaction.PaymentStatus != PaymentStatus.Pending)
            {
                throw new ArgumentException($"Transaction {paymentDto.TransactionId} is not pending payment");
            }

            // Update transaction
            transaction.PaymentMethod = paymentDto.PaymentMethod;
            transaction.PaymentStatus = PaymentStatus.Completed;
            transaction.PaymentReference = paymentDto.PaymentReference ?? Guid.NewGuid().ToString();
            transaction.UpdatedAt = DateTime.UtcNow;

            // Update vehicle status
            var vehicle = _vehicles.FirstOrDefault(v => v.Id == transaction.VehicleId);
            if (vehicle != null)
            {
                vehicle.Status = VehicleStatus.Exited;
                vehicle.UpdatedAt = DateTime.UtcNow;
            }

            return await Task.FromResult(new PaymentResponseDto
            {
                Success = true,
                TransactionId = transaction.Id,
                PaymentReference = transaction.PaymentReference,
                AmountCharged = transaction.Amount,
                ProcessedAt = DateTime.UtcNow
            });
        }

        public async Task<FeeCalculationDto> CalculateParkingFeeAsync(string vehicleId)
        {
            var vehicle = _vehicles.FirstOrDefault(v => v.Id == vehicleId);
            if (vehicle == null)
            {
                throw new ArgumentException($"Vehicle {vehicleId} not found");
            }

            var duration = (int)(DateTime.UtcNow - vehicle.EntryTime).TotalMinutes;
            var amount = CalculateFee(duration, vehicle.VehicleType);

            return await Task.FromResult(new FeeCalculationDto
            {
                VehicleId = vehicleId,
                Amount = amount,
                DurationMinutes = duration,
                Breakdown = new FeeBreakdown
                {
                    BaseRate = GetHourlyRate(vehicle.VehicleType),
                    Hours = Math.Max(1, (int)Math.Ceiling(duration / 60.0)),
                    HourlyCharge = amount,
                    MinimumFee = _configuration.Rules.MinimumFee,
                    Total = amount,
                    IsGracePeriod = duration <= _configuration.Rules.GracePeriodMinutes
                }
            });
        }

        public async Task<SystemStatistics> GetTodayStatisticsAsync()
        {
            var today = DateTime.Today;
            var todayTransactions = _transactions.Where(t => t.CreatedAt.Date == today).ToList();
            var currentVehicles = _vehicles.Where(v => v.Status == VehicleStatus.Parked).ToList();

            return await Task.FromResult(new SystemStatistics
            {
                Date = today,
                TotalVehicles = todayTransactions.Count,
                CurrentOccupancy = currentVehicles.Count,
                TotalRevenue = todayTransactions.Where(t => t.PaymentStatus == PaymentStatus.Completed).Sum(t => t.Amount),
                AverageStayTimeMinutes = todayTransactions.Any() ? 
                    (int)todayTransactions.Where(t => t.Duration.HasValue).Average(t => t.Duration!.Value) : 0,
                PeakOccupancy = currentVehicles.Count, // Simplified for demo
                CreatedAt = DateTime.UtcNow
            });
        }

        public async Task<SystemStatistics> GetStatisticsForDateAsync(DateTime date)
        {
            // Simplified implementation
            return await GetTodayStatisticsAsync();
        }

        public async Task<List<SystemStatistics>> GetStatisticsForPeriodAsync(DateTime startDate, DateTime endDate)
        {
            var stats = new List<SystemStatistics>();
            var current = startDate.Date;
            
            while (current <= endDate.Date)
            {
                stats.Add(await GetStatisticsForDateAsync(current));
                current = current.AddDays(1);
            }
            
            return stats;
        }

        public async Task<Dictionary<string, object>> GenerateReportAsync(string reportType, DateTime date)
        {
            var stats = await GetStatisticsForDateAsync(date);
            
            return new Dictionary<string, object>
            {
                ["reportType"] = reportType,
                ["date"] = date,
                ["statistics"] = stats,
                ["generatedAt"] = DateTime.UtcNow
            };
        }

        public async Task<ParkingConfiguration> GetConfigurationAsync()
        {
            return await Task.FromResult(_configuration);
        }

        public async Task<ParkingConfiguration> UpdateConfigurationAsync(ParkingConfiguration config)
        {
            _configuration = config;
            _configuration.UpdatedAt = DateTime.UtcNow;
            return await Task.FromResult(_configuration);
        }

        public async Task<int> GetCurrentOccupancyAsync()
        {
            return await Task.FromResult(_vehicles.Count(v => v.Status == VehicleStatus.Parked));
        }

        public async Task<int> GetAvailableSpotsAsync()
        {
            var occupancy = await GetCurrentOccupancyAsync();
            return Math.Max(0, _configuration.MaxCapacity - occupancy);
        }

        public async Task<bool> IsCapacityAvailableAsync()
        {
            return await GetAvailableSpotsAsync() > 0;
        }

        public async Task<bool> IsValidLicensePlateAsync(string licensePlate)
        {
            // Basic validation - can be enhanced
            return await Task.FromResult(!string.IsNullOrWhiteSpace(licensePlate) && licensePlate.Length >= 3);
        }

        public async Task<bool> IsVehicleCurrentlyParkedAsync(string licensePlate)
        {
            return await Task.FromResult(_vehicles.Any(v => 
                v.LicensePlate == licensePlate.ToUpper() && 
                v.Status == VehicleStatus.Parked));
        }

        public async Task<TimeSpan> GetParkingDurationAsync(string vehicleId)
        {
            var vehicle = _vehicles.FirstOrDefault(v => v.Id == vehicleId);
            if (vehicle == null)
            {
                throw new ArgumentException($"Vehicle {vehicleId} not found");
            }

            return await Task.FromResult(DateTime.UtcNow - vehicle.EntryTime);
        }

        private decimal CalculateFee(int durationMinutes, VehicleType vehicleType)
        {
            if (durationMinutes <= _configuration.Rules.GracePeriodMinutes)
            {
                return 0;
            }

            var hourlyRate = GetHourlyRate(vehicleType);
            var hours = Math.Max(1, (int)Math.Ceiling(durationMinutes / 60.0));
            var amount = hours * hourlyRate;

            return Math.Max(amount, _configuration.Rules.MinimumFee);
        }

        private decimal GetHourlyRate(VehicleType vehicleType)
        {
            return vehicleType switch
            {
                VehicleType.Car => _configuration.Rates.CarHourlyRate,
                VehicleType.Motorcycle => _configuration.Rates.MotorcycleHourlyRate,
                VehicleType.Truck => _configuration.Rates.TruckHourlyRate,
                _ => _configuration.Rates.CarHourlyRate
            };
        }
    }
}
