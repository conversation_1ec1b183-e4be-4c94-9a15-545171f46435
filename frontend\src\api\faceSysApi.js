export default {
    // 图片链接
    imgUrl: process.env.NODE_ENV !== "development" ?
        window.location.origin + "/img/" : "http://192.168.1.169/img/",
    // 获取菜单权限
    faceUserFindUserByName: "/faceUser/findUserByName",
    // logout: "/logout", // 退出登录
    // 上传照片
    uploadFile: "/upload/uploadFile", // 文件上传
    // 人脸系统公共参数
    parametersFindByCode: "/parameters/findByCode/", // 根据 code 获取配置信息
    parametersUploadByCode: "/parameters/uploadByCode/", // 根据 code 修改配置信息
    parametersFindAll: "/parameters/findAll/", // 获取全部配置信息
    parametersBatchUpdate: "/parameters/batchUpdate/", // 批量更新系统参数信息

    // 人脸设备接口
    deviceInfoDel: "/deviceInfo/delete", //  删除设备
    deviceInfoFindById: "/deviceInfo/findById/", //{id}  获取单条设备信息
    deviceInfoFindAll: "/deviceInfo/findAll", // 获取全部设备信息
    deviceInfoFindAllPage: "/deviceInfo/findAllPage", // 获取全部设备信息
    deviceInfoFindByName: "/deviceInfo/findByName/", //{id}  // 通过名字获取单条设备信息
    deviceInfoValitIp: "/deviceInfo/valitIp", // 验证IP是否已被使用
    deviceInfoFindByDeptId: "/deviceInfo/findByDeptId/", // 根据设备组 id 查询所有设备详细信息
    //---------------   海清   --------------------
    deviceInfoInsert: "/deviceInfo/insert", // 新增 设备
    deviceInfoUpdate: "/deviceInfo/update", // 更新 设备信息
    deviceInfoInit: "/deviceInfo/init", // 初始化相关的设备信息
    //-------------- 艾信达 ------------------------
    axdFaceInsert: "/axdFace/insert", // 初始化相关的设备信息
    axdFaceSearchDevice: "/axdFace/searchDevice", // 扫描设备
    axdFaceUpdate: "/axdFace/update", // 修改 axd 人脸设备数据
    // 用户
    findAllUser: "/faceUser/findAllUser", // 获取用户列表
    userInset: "/faceUser/insert", // 新增用户信息
    userUpdate: "/faceUser/update", // 更新用户信息
    userDelete: "/faceUser/delete", // 删除用户信息
    resetUserPwd: "/faceUser/resetUserPwd", // 初始化密码
    userValiaccount: "/faceUser/valiaccount", // 登录名唯一
    getUserInfo: "/faceUser/findUserByName", // 根据用户登录名，查询当前用户信息
    updatePwd: "/faceUser/updatePwd", // 修改密码
    // 用户组
    findAllRole: "/faceRole/findAllRole", //     获取所有的用户组信息
    isRepeatRole: "/faceRole/isRepeatRole", // 用户组名称 唯一
    addRole: "/faceRole/addRole", //     保存用户组
    updateRole: "/faceRole/updateRole", //     更新用户组
    deleteRole: "/faceRole/deleteRole", //     删除用户组数据

    updateRoleMenu: "/facePermission/updateRoleMenu", // 保存用户组权限
    getRoleMenuView: "/facePermission/getRoleMenuView", // 获取用户组权限

    // 开门条件
    openConditInsert: "/openCondit/insert",
    openConditUpdate: "/openCondit/update",
    openConditFindByDeviceId: "/openCondit/findByDeviceId/", //  {id}
    openConditFindOne: "/openCondit/findOne", //
    openConditPushAgain: "/openCondit/pushAgain", // 重新推送

    // 登录密码
    deviceInfoFindByDeviceId: "/deviceInfo/findByDeviceId/", //{id}
    deviceInfoUpdate: "/deviceInfo/update", // 设备基本用户设置

    // 中心连接
    montiorCenterFindByDeviceId: "/montiorCenter/findByDeviceId/", //{id} 中心连接查询
    montiorCenterUpdate: "/montiorCenter/update", // 中心连接修改
    montiorCenterRemotelyOpenDoor: "/montiorCenter/remotelyOpenDoor/", // 远程开门

    // 提示音及界面
    beepAndInterfaceFindByDeviceId: "/beepAndInterface/findByDeviceId/", // {id}
    beepAndInterfaceFindOne: "/beepAndInterface/findOne",
    beepAndInterfaceInsert: "/beepAndInterface/insert",
    beepAndInterfaceUpdate: "/beepAndInterface/update",
    beepAndInterfacePushAgain: "/beepAndInterface/pushAgain", // 重新推送

    // 人脸参数设置
    faceParamFindByDeviceId: "/faceParam/findByDeviceId/", //{id} 人脸 IPC 参数查询
    faceParamFindById: "/faceParam/findById/", //{id} IPC 参数设置
    faceParamInsert: "/faceParam/insert", // 设备操作
    faceParamUpdate: "/faceParam/update", // 设备操作

    // 文件升级
    // 系统参数设置
    sysParamFindByDeviceId: "/sysParam/findByDeviceId/", //{id} 提示声音及界面查询
    sysParamInsert: "/sysParam/insert", // 设备操作
    sysParamUpdate: "/sysParam/update", // 设备操作

    // 系统日志（取消系统日志，用操作日志替换）
    operLogFindAll: "/operLog/findAllLogs", // 获取操作日志
    operLogFindAllPage: "/operLog/findAllPage", // 获取操作日志
    operLogExportExcel: "/operLog/exportExcel", // 获取操作日志 导出 Excel
    // 设备操作日志
    devLogFindAll: "/devLog/findAll", // 获取操作日志
    devLogFindAllRealTime: "/devLog/findAllRealTime", // 实时获取推送日志
    devLogFindAllPage: "/devLog/findAllPage", // 获取操作日志
    // 设备异常推送记录
    deviceExceptionPushRecordFindAll: "deviceExceptionPushRecord/findAll", // 按条件查询全部
    deviceExceptionPushRecordFindAllPage: "deviceExceptionPushRecord/findAllPage", // 按条件分页查询

    // HTTP 订阅
    httpSubFindOne: "/httpSub/findOne", // 查询 HTTP 订阅数据
    httpSubInsert: "/httpSub/insert", // 新增 HTTP 订阅数据
    httpSubUpdate: "/httpSub/update", // 修改 HTTP 订阅数据
    httpSubPushAgain: "/httpSub/pushAgain", // 重新推送
    // 监控中心
    montiorCenterFindByControlRecordRatio: "/montiorCenter/findByControlRecordRatio", // 男女比例和年龄段比例
    montiorCenterGetDeviceState: "/montiorCenter/getDeviceState", // 设备在线状态
    // 名单管理
    personDel: "/person/delete/", //{id} 删除单条数据
    personDels: "/person/deletes", // 批量删除
    personFindAll: "/person/findAll", // 分页查询
    personFindAllPage: "/person/findAllPage", // 分页查询
    personFinById: "/person/findById/", //{id} 根据 id 查询单条数据
    personInsert: "/person/insert", // 新增数据
    personUpdate: "/person/update", // 更新数据。
    personBindDeptIssue: "/person/bindDeptIssue", // {id}  // 绑定设备组
    personGetBindDevGroup: "/person/getBindDevGroup/", // {perId}   // 查询当前名单绑定的设备组
    personFindByDeviceIdIsNull: "/person/findByDeviceIdIsNull", //  // 查询未绑定设备组的名单信息
    personPushAgain: "/person/pushAgain", //  // 重新推送
    /**
        根据 身份证号码查询是否有数据，返回 :  [{ id: Number }]。
        @RequestBody Array<cardNum>  身份证号码
        @Callback Array<Object<Number>> id 
        **/
    findAllCardIds: "/person/findAllCardIds",
    // 下载 excel 模板
    downLoadCPerTemp: "/person/downLoadCPerTemp", // 下载 excel 模板
    // 批量导入名单信息
    personImportExcelData: "/person/importExcelData",

    // 部门管理
    departDel: "/depart/delete/", //{id} 部门模块
    departFindAll: "/depart/findAll", // 部门模块
    departFindAllPage: "/depart/findAllPage", // 部门模块
    departInsert: "/depart/insert", // 部门模块
    departSaveDeptDev: "/depart/saveDeptDev/", //{id} 部门模块
    departUpdate: "/depart/update", // 部门模块
    departFindByDepartName: "/depart/findByDepartName/", //  {departName} 部门模块
    departGetDeptDev: "/depart/getDeptDev/", // {departId}  // 部门模块
    departGetDeptPerson: "/depart/getDeptPerson/", // {departId}  查询所有部门关联的名单列表
    departUntieDepartParson: "/depart/untieDepartParson/", // {departId}  解绑部门关联的名单列表

    // 抓拍记录
    // snapShotFindAll: "/snapShot/findAll",    // 获取抓拍记录
    // snapShotFindAllPage: "/snapShot/findAllPage",    // 获取抓拍记录
    // snapShotFindById:"/snapShot/findById/",  //{id} 获取抓拍记录
    // // 控制记录
    controlFindAll: "/control/findAll", // 获取控制记录
    controlFindAllPage: "/control/findAllPage", // 获取控制记录
    controlFindById: "/control/findById/", //{id} 获取控制记录

    // 人脸抓拍控制
    axdFaceCaptureFindOne: "/axdFaceCapture/findOne",
    axdFaceCaptureUpdate: "/axdFaceCapture/update",
    axdFaceCapturePushAgain: "/axdFaceCapture/pushAgain",
    // 闸机控制
    axdOpenGateFindOne: "/axdOpenGate/findOne",
    axdOpenGateUpdate: "/axdOpenGate/update",
    axdOpenGatePushAgain: "/axdOpenGate/pushAgain",
    // 屏幕显示设置
    axdViewControlFindOne: "/axdViewControl/findOne",
    axdViewControlUpdate: "/axdViewControl/update",
    axdViewControlPushAgain: "/axdViewControl/pushAgain",
    //------答案设备相关接口----------------------------------
    daFaceInit: "/daFace/init", // 初始化设备信息
    daFaceInsert: "/daFace/insert", // 新增设备
    daFaceUpdate: "/daFace/update", // 编辑设备
    findDaParameter: "/daFace/findParameter", // 获取人脸参数
    saveDaParameter: "/daFace/saveParameter", // 保存人脸参数
    deviceReboot: "/daFace/deviceReboot", // 重启设备
    uploadPic: "/upload/uploadPic", // 上传屏保图片
    setAdvertPicture: "/daFace/setAdvertPicture", // 设置屏保图片
};