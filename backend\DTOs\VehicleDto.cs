using System.ComponentModel.DataAnnotations;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.DTOs
{
    public class VehicleDto
    {
        public string Id { get; set; } = string.Empty;
        public string LicensePlate { get; set; } = string.Empty;
        public DateTime EntryTime { get; set; }
        public DateTime? ExitTime { get; set; }
        public string? ParkingSpot { get; set; }
        public VehicleType VehicleType { get; set; }
        public string? ImageUrl { get; set; }
        public VehicleStatus Status { get; set; }
    }
    
    public class CreateVehicleEntryDto
    {
        [Required]
        [StringLength(20, MinimumLength = 1)]
        public string LicensePlate { get; set; } = string.Empty;
        
        [Required]
        public VehicleType VehicleType { get; set; }
        
        [StringLength(500)]
        public string? ImageUrl { get; set; }
        
        [StringLength(10)]
        public string? ParkingSpot { get; set; }
    }
    
    public class VehicleExitDto
    {
        [Required]
        [StringLength(20, MinimumLength = 1)]
        public string LicensePlate { get; set; } = string.Empty;
        
        public DateTime? ExitTime { get; set; }
    }
    
    public class VehicleSearchDto
    {
        public string? LicensePlate { get; set; }
        public VehicleType? VehicleType { get; set; }
        public VehicleStatus? Status { get; set; }
        public DateTime? EntryTimeFrom { get; set; }
        public DateTime? EntryTimeTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
    
    public class VehicleListResponseDto
    {
        public List<VehicleDto> Vehicles { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }
}
