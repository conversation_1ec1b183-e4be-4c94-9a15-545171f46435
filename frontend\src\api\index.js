import Vue from 'vue'
import Axios from 'axios'
import VueAxios from 'vue-axios'
import qs from 'qs'
import parkSysApi from './parkSysApi.js'
import faceSysApi from "./faceSysApi.js"
import daApi from "./daApi.js"
import accountApi from "./accountApi.js"
import { Message } from 'view-design';
import cookies from '@/libs/util.cookies'
import { clone } from '@/utils/clone'
import store from '@/store'
import Setting from '@/setting';
import router from "@/router"
Vue.prototype.$api = parkSysApi
Vue.prototype.$faceApi = faceSysApi
import util from '@/libs/util';

let axiosData = {}
    // http 请求拦截器
Axios.interceptors.request.use(
        async config => {
            // 只在开发模式下打印 log 
            if (process.env.NODE_ENV === 'development') {
                util.log.capsuleDark('请求路径', config.url);
                util.log.info('>>>>>> axios 请求实体 >>>>>>');
                console.log(JSON.stringify(config));
                console.log(config, 55);
                util.log.info('>>>>>> Request >>>>>>');
            }
            if (config[0] && config[0].indexOf('_') !== -1) {
                // 弹出层 loading
                store.commit('_loading', true)
            } else if (!config[0] || (config[0] && config[0].indexOf('non') === -1)) { // 判断是否为非初始化的 loading
                // 初始化 loading
                store.commit('loading', true)
            }
            // 错误码范围
            config.validateStatus = function(status) {
                    return (status < 500) // 非 500 都正确返回到响应拦截。
                }
                // 语言
            let locale = (store.state.admin.i18n && store.state.admin.i18n.locale && (typeof store.state.admin.i18n.locale === "string") && store.state.admin.i18n.locale.replace("-", "_"))
            config.headers['lang'] = locale || 'zh_CN'

            // 默认空值替换为 null , 有 string 标识则把空值替换为空字符串
            if (config[0] && config[0].indexOf('string') !== -1) config.data =
                clone(config.data, "string")
            else config.data = clone(config.data)
                // 添加 accessToken
                // config.headers['Authorization'] = "Bearer " + cookies.get("token")
                !config.headers['accessToken'] && (config.headers['accessToken'] = cookies.get("token"))
                // 默认 json 传参
            config.headers['content-type'] = 'application/json;charset=UTF-8'
                // form 传参
            if (config[0] && config[0].indexOf('form') !== -1) {
                config.data = qs.stringify(config.data)
                config.headers['Content-type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
                    // query 传参
            } else if (config[0] && config[0].indexOf('query') !== -1) {
                config.url = `${config.url}?${qs.stringify(config.data)}`
                config.headers['content-type'] = 'application/x-www-form-urlencoded'
                    // 下载 excel
            } else if (config[0] && config[0].indexOf('download') !== -1) {
                config.data && (JSON.stringify(config.data) !== "{}") && (config.url = `${config.url}?${qs.stringify(config.data)}`)
                config.responseType = 'arraybuffer'
                    // 上传文件
            } else if (config[0] && config[0].indexOf('upload') !== -1) {
                config.headers['content-type'] = 'multipart/form-data'
                const formData = new FormData()
                formData.append('file', new Blob([config.file], { type: config.file.type }), config.file.name)
                config.file = formData
                config.data = config.file
            }
            return config
        },
        err => {
            store.commit('loading', false)
            Promise.reject(err)
        }
    )
    // http响应拦截器
Axios.interceptors.response.use(
    response => {
        // 只在开发模式下打印 log
        if (process.env.NODE_ENV === 'development') {
            util.log.capsule('服务器响应', response.config.url, "success");
            util.log.success('>>>>>> axios 响应实体 >>>>>>');
            if (response.data instanceof ArrayBuffer) { // 二进制文件
                console.log(response.data)
            } else {
                util.log.response(response.data)
            }
            util.log.success('>>>>>> Response >>>>>>');
        }
        // console.log(response)
        setTimeout(() => {
                if (response.config['0'] && response.config['0'].indexOf('non') !== -1) {
                    store.commit('loadingList', false) // 关闭loading
                } else {
                    store.commit('loading', false) // 关闭loading
                }
            }, 200)
            // 下载文件处理
        if (response.config['0'] && response.config['0'].indexOf('download') !== -1) { // 下载 excel
            if (response.data && response.data.status == 0) {
                return false
            } else {
                if (response.data instanceof ArrayBuffer) {
                    if (response.data.byteLength > 100) {
                        return response.data
                    } else {
                        let data = new Uint8Array(response.data);
                        let str = String.fromCharCode.apply(null, data);
                        if (str.indexOf("status") !== -1) {
                            let data = JSON.parse(str)
                            if (data.status === 401 || data.status === 2 || data.status === 3 || data.error == "invalid_token") {
                                store.state.messageTimeout && (store.commit('messageTimeout'), Message.error('登录已过期 ！'))
                                cookies.set("token", "", { expires: 0 })
                                router.push("/login")
                                return false
                            }
                            Message.error("下载文件异常！")
                            return false
                        }
                    }
                }
                Message.error("下载文件异常")
                return false
            }
        }
        if (response.data.status === 1) {
            if (response.data) {
                if (!response.data.msg) {
                    response.data.msg = "操作成功！"
                }
                return response.data
            }
            return { msg: "操作成功！" }
        } else if (response.data.status === 401 || response.data.status === 2 || response.data.status === 3 || response.data.error == "invalid_token") {
            store.state.messageTimeout && (store.commit('messageTimeout'), Message.error(response.data.msg || '登录已过期 ！'))
            cookies.set("token", "", { expires: 0 })
            router.push("/login")
            return false
        } else if (response.config.url && response.config.url.indexOf("/oauth/token?") !== -1 && response.data.access_token) {
            return response.data
        } else if (response.data.error === "invalid_grant") {
            Message.error("用户名或密码错误！")
            return false
        } else if (response.request.responseURL && response.request.responseURL.indexOf('JSESSIONID=') !== -1) {
            cookies.set("token", "", { expires: 0 })
            router.push("/login")
            Message.error("登录已过期！")
            return false
        } else if (response.data.result == 0) {
            return response.data
        } else {
            store.state.messageTimeout && (store.commit('messageTimeout'), Message.error(response.data.msg || response.data.error_description || '未知错误！'))
            return false
        }
    },
    error => {
        console.log(error)
        store.commit('loading', false)
        if (!store.state.isErrorMsgShow) {
            return
        }
        store.state.messageTimeout && (store.commit('messageTimeout'), Message.error('未知错误 ！'))
        Promise.reject(error)
    }
)
Vue.use(VueAxios, Axios)

const Apis = {}
Object.keys(parkSysApi).forEach(key => Apis[key] = (data, type, params = "", timeout = 60 * 1000, onUploadProgress = () => {}) => {
    Vue.axios.defaults.baseURL = "/yard"
    Vue.axios.defaults.timeout = timeout
    if (type && type.indexOf('upload') !== -1) { // 上传文件
        return Vue.axios({
            method: "post",
            url: parkSysApi[key] + params,
            file: data,
            onUploadProgress: onUploadProgress,
            "0": type
        })
    }
    return Vue.axios.post(parkSysApi[key] + params, data, type)
})

// 通过 code 再去获取 
// Apis.oauthToken = (data = {}, type = "") => {
//   Vue.axios.defaults.baseURL = process.env.NODE_ENV === 'development' ? "http://192.168.1.14:8080/" : (location.origin + ":8080/") // 开发环境用 192.168.59:8080
//   return Vue.axios.post(accountApi.oauthToken + `?grant_type=password&client_id=yard&client_secret=yard-8888`, data, type)
// }

const ApisFaceSys = {}
Object.keys(faceSysApi).forEach(key => ApisFaceSys[key] = (data, type, params = "", timeout = 60 * 1000, onUploadProgress = () => {}) => {
    Vue.axios.defaults.baseURL = "/api2/face"
    Vue.axios.defaults.timeout = timeout
    if (type && type.indexOf('upload') !== -1) { // 上传文件
        return Vue.axios({
            method: "post",
            url: faceSysApi[key] + params,
            file: data,
            onUploadProgress: onUploadProgress,
            "0": type
        })
    }
    return Vue.axios.post(faceSysApi[key] + params, data, type)
})

const ApisDaSys = {}
Object.keys(daApi).forEach(key => ApisDaSys[key] = (data, type, params = "", timeout = 60 * 1000, onUploadProgress = () => {}) => {
    Vue.axios.defaults.baseURL = "/daFace"
    Vue.axios.defaults.timeout = timeout
    if (type && type.indexOf('upload') !== -1) { // 上传文件
        return Vue.axios({
            method: "post",
            url: daApi[key] + params,
            file: data,
            onUploadProgress: onUploadProgress,
            "0": type
        })
    }
    return Vue.axios.post(daApi[key] + params, data, type)
})
export {
    Apis,
    ApisFaceSys,
    ApisDaSys
}