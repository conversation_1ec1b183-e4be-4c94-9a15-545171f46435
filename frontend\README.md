# Parking Booth Frontend

A Vue 3 frontend application for the parking booth management system, built with TypeScript, Vite, Vue Router, and Pinia.

## Features

- **Dashboard**: Real-time system overview with statistics and hardware status
- **Parking Operations**: Vehicle entry/exit management with camera integration
- **Payment Processing**: Support for cash, credit card, and Macau Pass payments
- **Hardware Status**: Monitor and control printer, camera, card reader, and ground sensors
- **Reports & Analytics**: Generate and view parking reports with visual analytics
- **Settings**: Configure parking rates, hardware settings, and system preferences

## Technology Stack

- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vite** for fast development and building
- **Vue Router 4** for navigation
- **Pinia** for state management
- **CSS Grid & Flexbox** for responsive layouts

## Project Structure

```
src/
├── components/          # Reusable Vue components
│   └── NavigationHeader.vue
├── views/              # Page components
│   ├── DashboardView.vue
│   ├── ParkingOperationsView.vue
│   ├── PaymentView.vue
│   ├── HardwareStatusView.vue
│   ├── ReportsView.vue
│   ├── SettingsView.vue
│   └── NotFoundView.vue
├── stores/             # Pinia stores
│   ├── hardware.ts     # Hardware state management
│   └── parking.ts      # Parking operations state
├── services/           # API services
│   ├── hardwareService.ts
│   └── parkingService.ts
├── router/             # Vue Router configuration
│   └── index.ts
├── types/              # TypeScript type definitions
├── assets/             # Static assets
├── App.vue             # Root component
└── main.ts             # Application entry point
```

## Hardware Integration

The frontend provides interfaces for:

### Printer
- Print parking tickets and receipts
- Monitor paper levels and status
- Test printing functionality

### Camera
- Capture vehicle images on entry/exit
- Configure resolution settings
- View capture history

### Macau Pass Card Reader
- Read card information and balance
- Process card payments
- Monitor reader status

### Ground Sensors
- Detect vehicle presence
- Configure sensitivity settings
- Real-time status monitoring

## API Integration

The frontend communicates with the .NET backend API at `http://localhost:5000/api` for:

- Vehicle entry/exit operations
- Payment processing
- Hardware control and status
- Report generation
- Configuration management

## Development Setup

### Prerequisites
- Node.js 18+ and npm
- .NET 7 backend running on localhost:5000

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:5173`

### Build for Production
```bash
# Type check
npm run type-check

# Build for production
npm run build

# Preview production build
npm run preview
```

## Configuration

### Environment Variables
Create a `.env` file in the frontend directory:

```env
VITE_API_BASE_URL=http://localhost:5000/api
VITE_BOOTH_ID=001
```

### API Endpoints
The frontend expects the following API endpoints:

- `GET /api/hardware/status` - Hardware status
- `POST /api/hardware/printer/print` - Print operations
- `POST /api/hardware/camera/capture` - Image capture
- `POST /api/hardware/cardreader/read` - Card reading
- `GET /api/parking/current` - Current vehicles
- `POST /api/parking/entry` - Vehicle entry
- `POST /api/parking/exit` - Vehicle exit
- `POST /api/parking/payment` - Payment processing

## State Management

### Hardware Store
Manages hardware device states and operations:
- Device connection status
- Real-time status monitoring
- Hardware control operations
- Error handling and recovery

### Parking Store
Manages parking operations and data:
- Current vehicles tracking
- Transaction history
- Payment processing
- Statistics and reporting

## Responsive Design

The application is fully responsive and optimized for:
- Desktop displays (1920x1080 and higher)
- Tablet devices (768px and up)
- Touch interfaces for kiosk operation

## Error Handling

- Global error handler for hardware failures
- Network error recovery
- User-friendly error messages
- Automatic retry mechanisms

## Testing

```bash
# Run unit tests (when implemented)
npm run test

# Run e2e tests (when implemented)
npm run test:e2e
```

## Deployment

### Production Build
```bash
npm run build
```

The built files will be in the `dist/` directory and can be served by any static file server.

### Docker Deployment
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Follow Vue 3 Composition API patterns
2. Use TypeScript for all new code
3. Maintain responsive design principles
4. Add proper error handling
5. Update documentation for new features

## License

[Add your license information here]
