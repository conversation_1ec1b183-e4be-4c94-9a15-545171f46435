using ParkingBoothApi.DTOs;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Interfaces
{
    public interface IExitService
    {
        /// <summary>
        /// 处理车辆出口检测
        /// </summary>
        Task<ExitVehicleDetectionDto> ProcessVehicleDetectionAsync(ExitVehicleDetectionDto detection);

        /// <summary>
        /// 进行出场决策
        /// </summary>
        Task<ExitDecisionDto> MakeExitDecisionAsync(ExitVehicleDetectionDto detection);

        /// <summary>
        /// 处理支付
        /// </summary>
        Task<ExitProcessResultDto> ProcessPaymentAsync(PaymentProcessDto paymentDto);

        /// <summary>
        /// 处理验票出场
        /// </summary>
        Task<ExitProcessResultDto> ProcessTicketExitAsync(TicketExitDto ticketDto);

        /// <summary>
        /// 处理刷卡出场
        /// </summary>
        Task<ExitProcessResultDto> ProcessCardExitAsync(CardExitDto cardDto);

        /// <summary>
        /// 处理人工操作
        /// </summary>
        Task<ExitProcessResultDto> ProcessManualActionAsync(ManualExitActionDto actionDto);

        /// <summary>
        /// 控制出口道闸
        /// </summary>
        Task<bool> ControlExitBarrierAsync(string exitId, BarrierAction action, string operatorId, string reason);

        /// <summary>
        /// 获取出口状态
        /// </summary>
        Task<List<ExitStatusDto>> GetExitStatusAsync();

        /// <summary>
        /// 获取当前班次统计
        /// </summary>
        Task<ShiftSummaryDto> GetCurrentShiftSummaryAsync(string operatorId);

        /// <summary>
        /// 获取车位信息
        /// </summary>
        Task<ParkingSpaceInfoDto> GetParkingSpaceInfoAsync();

        /// <summary>
        /// 获取可用抵用券
        /// </summary>
        Task<List<CouponDto>> GetAvailableCouponsAsync();
    }
}
