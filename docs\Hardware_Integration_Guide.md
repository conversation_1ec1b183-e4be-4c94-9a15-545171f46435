# Hardware Integration Guide

## Overview

The Parking Booth System is designed to integrate with various hardware components commonly found in parking facilities. This guide provides detailed information on hardware requirements, integration patterns, and troubleshooting.

## Supported Hardware Components

### 1. Thermal Printer
**Purpose**: Print parking tickets and payment receipts

**Requirements**:
- Thermal printer with ESC/POS command support
- USB or Serial connection
- Paper width: 80mm
- Auto-cutter support (recommended)

**Integration**:
- Driver installation required
- Configure COM port or USB connection
- Test print functionality during setup

**API Endpoints**:
- `POST /api/hardware/printer/print` - Print tickets/receipts
- `GET /api/hardware/printer/status` - Check printer status
- `POST /api/hardware/printer/test` - Test print functionality

### 2. IP Camera
**Purpose**: Capture vehicle images for license plate recognition

**Requirements**:
- IP camera with HTTP/RTSP support
- Minimum resolution: 1280x720 (HD)
- Recommended resolution: 1920x1080 (Full HD)
- Night vision capability (recommended)
- Weatherproof housing for outdoor installation

**Integration**:
- Network configuration (IP address, credentials)
- Camera positioning for optimal license plate capture
- Image storage configuration

**API Endpoints**:
- `POST /api/hardware/camera/capture` - Capture image
- `GET /api/hardware/camera/status` - Check camera status
- `POST /api/hardware/camera/test` - Test capture functionality

### 3. Macau Pass Card Reader
**Purpose**: Read Macau Pass cards for payment processing

**Requirements**:
- NFC/RFID card reader compatible with Macau Pass
- USB or Serial connection
- SDK/API for card communication
- Secure payment processing capability

**Integration**:
- Driver and SDK installation
- Payment gateway configuration
- Security certificate setup

**API Endpoints**:
- `POST /api/hardware/cardreader/read` - Read card information
- `POST /api/hardware/cardreader/payment` - Process payment
- `GET /api/hardware/cardreader/status` - Check reader status

### 4. Ground Sensor
**Purpose**: Detect vehicle presence for automated entry/exit

**Requirements**:
- Inductive loop sensor or ultrasonic sensor
- Controller unit with digital/analog output
- Weatherproof installation
- Adjustable sensitivity settings

**Integration**:
- Sensor calibration and positioning
- Controller configuration
- Signal processing setup

**API Endpoints**:
- `GET /api/hardware/groundsensor/detect` - Check vehicle presence
- `GET /api/hardware/groundsensor/status` - Check sensor status
- `POST /api/hardware/groundsensor/test` - Test detection functionality

## Hardware Communication Protocols

### Serial Communication (RS-232/RS-485)
```csharp
// Example serial port configuration
var serialPort = new SerialPort("COM1", 9600, Parity.None, 8, StopBits.One);
serialPort.Handshake = Handshake.None;
serialPort.ReadTimeout = 5000;
serialPort.WriteTimeout = 5000;
```

### USB Communication
```csharp
// Example USB device detection
var devices = UsbDevice.AllDevices.Where(d => 
    d.Vid == 0x1234 && d.Pid == 0x5678);
```

### Network Communication (TCP/IP)
```csharp
// Example HTTP camera communication
var httpClient = new HttpClient();
var response = await httpClient.GetAsync("http://camera-ip/capture");
```

## Configuration Files

### Hardware Configuration (appsettings.json)
```json
{
  "Hardware": {
    "Printer": {
      "Type": "Thermal",
      "ConnectionType": "USB",
      "DeviceId": "VID_04B8&PID_0202",
      "Settings": {
        "PaperWidth": 80,
        "AutoCut": true,
        "Encoding": "UTF-8"
      }
    },
    "Camera": {
      "Type": "IP",
      "IpAddress": "*************",
      "Username": "admin",
      "Password": "password",
      "Settings": {
        "Resolution": "1920x1080",
        "Quality": 85,
        "NightVision": true
      }
    },
    "CardReader": {
      "Type": "MacauPass",
      "ConnectionType": "USB",
      "DeviceId": "VID_072F&PID_2200",
      "Settings": {
        "Timeout": 30000,
        "RetryAttempts": 3
      }
    },
    "GroundSensor": {
      "Type": "Inductive",
      "ConnectionType": "Serial",
      "ComPort": "COM2",
      "Settings": {
        "Sensitivity": 5,
        "DebounceTime": 500
      }
    }
  }
}
```

## Installation and Setup

### 1. Physical Installation

**Printer**:
- Mount securely in booth
- Ensure paper roll accessibility
- Connect power and data cables
- Test paper feed mechanism

**Camera**:
- Position for optimal license plate view
- Ensure adequate lighting
- Weatherproof all connections
- Configure network settings

**Card Reader**:
- Mount at comfortable height for users
- Ensure clear card placement area
- Connect to secure payment network
- Test card detection range

**Ground Sensor**:
- Install in vehicle path
- Calibrate detection zone
- Ensure proper grounding
- Test with different vehicle types

### 2. Software Configuration

**Driver Installation**:
```bash
# Install printer drivers
sudo apt-get install cups-driver-escpos

# Install camera drivers
sudo apt-get install v4l-utils

# Install card reader SDK
sudo dpkg -i macaupass-sdk.deb
```

**Service Configuration**:
```csharp
// Register hardware services
services.AddSingleton<IPrinterService, ThermalPrinterService>();
services.AddSingleton<ICameraService, IpCameraService>();
services.AddSingleton<ICardReaderService, MacauPassService>();
services.AddSingleton<ISensorService, GroundSensorService>();
```

## Error Handling and Recovery

### Common Error Scenarios

**Printer Errors**:
- Paper jam or empty
- Communication timeout
- Driver issues

**Camera Errors**:
- Network connectivity
- Authentication failure
- Image capture timeout

**Card Reader Errors**:
- Card read failure
- Payment processing error
- Communication timeout

**Sensor Errors**:
- Calibration drift
- Environmental interference
- Connection issues

### Recovery Strategies

```csharp
public async Task<bool> RecoverDevice(string deviceType)
{
    try
    {
        switch (deviceType.ToLower())
        {
            case "printer":
                await ResetPrinter();
                await TestPrinter();
                break;
            case "camera":
                await ReconnectCamera();
                await TestCapture();
                break;
            case "cardreader":
                await ResetCardReader();
                await TestCardRead();
                break;
            case "sensor":
                await RecalibrateSensor();
                await TestDetection();
                break;
        }
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to recover {DeviceType}", deviceType);
        return false;
    }
}
```

## Monitoring and Diagnostics

### Health Monitoring
```csharp
public class HardwareHealthMonitor : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await CheckAllDevices();
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
        }
    }
    
    private async Task CheckAllDevices()
    {
        var tasks = new[]
        {
            CheckPrinterHealth(),
            CheckCameraHealth(),
            CheckCardReaderHealth(),
            CheckSensorHealth()
        };
        
        await Task.WhenAll(tasks);
    }
}
```

### Diagnostic Commands
```bash
# Check device connectivity
curl -X POST http://localhost:5000/api/hardware/diagnostics

# Test individual devices
curl -X POST http://localhost:5000/api/hardware/printer/test
curl -X POST http://localhost:5000/api/hardware/camera/test
curl -X POST http://localhost:5000/api/hardware/cardreader/test
curl -X POST http://localhost:5000/api/hardware/groundsensor/test
```

## Security Considerations

### Data Protection
- Encrypt sensitive card data
- Secure communication channels
- Regular security updates
- Access control for hardware settings

### Physical Security
- Tamper-evident enclosures
- Secure mounting
- Cable protection
- Environmental monitoring

## Maintenance Procedures

### Daily Checks
- Verify all devices are online
- Check paper levels
- Test basic functionality
- Review error logs

### Weekly Maintenance
- Clean camera lens
- Check cable connections
- Update device firmware
- Calibrate sensors

### Monthly Maintenance
- Deep clean all devices
- Replace consumables
- Performance testing
- Security audit

## Troubleshooting Guide

### Device Not Responding
1. Check power connections
2. Verify communication cables
3. Restart device service
4. Check device drivers
5. Review system logs

### Poor Performance
1. Check network bandwidth
2. Verify device settings
3. Update firmware/drivers
4. Clean hardware components
5. Optimize configuration

### Intermittent Failures
1. Check environmental conditions
2. Verify power stability
3. Test cable integrity
4. Monitor system resources
5. Review error patterns

## Support and Documentation

### Vendor Contacts
- Printer Support: <EMAIL>
- Camera Support: <EMAIL>
- Card Reader Support: <EMAIL>
- Sensor Support: <EMAIL>

### Documentation Resources
- Hardware manuals: `/docs/hardware/`
- API documentation: `/docs/api/`
- Troubleshooting guides: `/docs/troubleshooting/`
- Video tutorials: https://training.parkingbooth.com
