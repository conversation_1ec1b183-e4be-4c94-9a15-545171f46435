using System.ComponentModel.DataAnnotations;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.DTOs
{
    public class HardwareStatusDto
    {
        public PrinterStatusDto Printer { get; set; } = new();
        public CameraStatusDto Camera { get; set; } = new();
        public CardReaderStatusDto CardReader { get; set; } = new();
        public GroundSensorStatusDto GroundSensor { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }
    
    public class PrinterStatusDto
    {
        public bool Connected { get; set; }
        public string Status { get; set; } = "offline";
        public int PaperLevel { get; set; }
        public string? LastError { get; set; }
        public DateTime? LastPrintTime { get; set; }
    }
    
    public class CameraStatusDto
    {
        public bool Connected { get; set; }
        public string Status { get; set; } = "offline";
        public string Resolution { get; set; } = "1920x1080";
        public DateTime? LastCapture { get; set; }
    }
    
    public class CardReaderStatusDto
    {
        public bool Connected { get; set; }
        public string Status { get; set; } = "offline";
        public DateTime? LastTransaction { get; set; }
        public decimal? Balance { get; set; }
    }
    
    public class GroundSensorStatusDto
    {
        public bool Connected { get; set; }
        public string Status { get; set; } = "offline";
        public bool VehicleDetected { get; set; }
        public DateTime? LastDetection { get; set; }
    }
    
    public class PrintTicketDto
    {
        [Required]
        public string Type { get; set; } = string.Empty; // "entry", "receipt", "test"
        
        public string? LicensePlate { get; set; }
        public decimal? Amount { get; set; }
        public string? PaymentMethod { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object>? AdditionalData { get; set; }
    }
    
    public class CaptureImageDto
    {
        public string? Purpose { get; set; } = "entry"; // "entry", "exit", "manual"
        public string? VehicleId { get; set; }
        public string? LicensePlate { get; set; }
    }
    
    public class ImageCaptureResponseDto
    {
        public bool Success { get; set; }
        public string? ImageUrl { get; set; }
        public string? ImagePath { get; set; }
        public DateTime Timestamp { get; set; }
        public string? Message { get; set; }
    }
    
    public class CardReadResponseDto
    {
        public bool Success { get; set; }
        public string? CardId { get; set; }
        public decimal Balance { get; set; }
        public string CardType { get; set; } = string.Empty;
        public DateTime ReadTime { get; set; } = DateTime.UtcNow;
        public string? Message { get; set; }
    }
    
    public class CardPaymentDto
    {
        [Required]
        public string CardId { get; set; } = string.Empty;
        
        [Required]
        [Range(0.01, 10000)]
        public decimal Amount { get; set; }
        
        public string? TransactionId { get; set; }
    }
    
    public class CardPaymentResponseDto
    {
        public bool Success { get; set; }
        public string? TransactionId { get; set; }
        public decimal NewBalance { get; set; }
        public decimal AmountCharged { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
        public string? Message { get; set; }
    }
    
    public class VehicleDetectionResponseDto
    {
        public bool VehicleDetected { get; set; }
        public DateTime CheckTime { get; set; } = DateTime.UtcNow;
        public int SensorReading { get; set; }
        public string? Message { get; set; }
    }
    
    public class HardwareDiagnosticsDto
    {
        public List<DiagnosticResult> Results { get; set; } = new();
        public string OverallHealth { get; set; } = "unknown";
        public DateTime RunTime { get; set; } = DateTime.UtcNow;
        public TimeSpan Duration { get; set; }
    }
    
    public class DiagnosticResult
    {
        public string DeviceType { get; set; } = string.Empty;
        public string TestName { get; set; } = string.Empty;
        public bool Passed { get; set; }
        public string? Message { get; set; }
        public Dictionary<string, object>? Details { get; set; }
    }
    
    public class HardwareCommandDto
    {
        [Required]
        public string DeviceType { get; set; } = string.Empty;
        
        [Required]
        public string Command { get; set; } = string.Empty;
        
        public Dictionary<string, object>? Parameters { get; set; }
    }
    
    public class HardwareResponseDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public Dictionary<string, object>? Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
