using ParkingBoothApi.DTOs;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Interfaces
{
    public interface IHardwareService
    {
        // Status Management
        Task<HardwareStatusDto> GetAllHardwareStatusAsync();
        Task<PrinterStatusDto> GetPrinterStatusAsync();
        Task<CameraStatusDto> GetCameraStatusAsync();
        Task<CardReaderStatusDto> GetCardReaderStatusAsync();
        Task<GroundSensorStatusDto> GetGroundSensorStatusAsync();
        Task RefreshHardwareStatusAsync();
        
        // Printer Operations
        Task<HardwareResponseDto> PrintTicketAsync(PrintTicketDto printDto);
        Task<HardwareResponseDto> TestPrinterAsync();
        Task<HardwareResponseDto> CheckPrinterPaperAsync();
        Task<HardwareResponseDto> ResetPrinterAsync();
        
        // Camera Operations
        Task<ImageCaptureResponseDto> CaptureImageAsync(CaptureImageDto captureDto);
        Task<HardwareResponseDto> TestCameraAsync();
        Task<HardwareResponseDto> SetCameraResolutionAsync(string resolution);
        Task<HardwareResponseDto> ResetCameraAsync();
        
        // Card Reader Operations
        Task<CardReadResponseDto> ReadCardAsync();
        Task<CardPaymentResponseDto> ProcessCardPaymentAsync(CardPaymentDto paymentDto);
        Task<HardwareResponseDto> TestCardReaderAsync();
        Task<HardwareResponseDto> ResetCardReaderAsync();
        
        // Ground Sensor Operations
        Task<VehicleDetectionResponseDto> CheckVehiclePresenceAsync();
        Task<HardwareResponseDto> SetSensorSensitivityAsync(int sensitivity);
        Task<HardwareResponseDto> TestGroundSensorAsync();
        Task<HardwareResponseDto> ResetGroundSensorAsync();
        
        // Diagnostics and Maintenance
        Task<HardwareDiagnosticsDto> RunFullDiagnosticsAsync();
        Task<HardwareResponseDto> ResetAllHardwareAsync();
        Task<HardwareResponseDto> ExecuteHardwareCommandAsync(HardwareCommandDto commandDto);
        
        // Configuration
        Task<HardwareConfiguration> GetHardwareConfigurationAsync();
        Task<HardwareConfiguration> UpdateHardwareConfigurationAsync(HardwareConfiguration config);
        
        // Health Monitoring
        Task<bool> IsHardwareHealthyAsync();
        Task<Dictionary<string, bool>> GetDeviceHealthStatusAsync();
        Task StartHealthMonitoringAsync();
        Task StopHealthMonitoringAsync();
    }
}
