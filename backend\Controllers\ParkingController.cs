using Microsoft.AspNetCore.Mvc;
using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ParkingController : ControllerBase
    {
        private readonly IParkingService _parkingService;
        private readonly ILogger<ParkingController> _logger;

        public ParkingController(IParkingService parkingService, ILogger<ParkingController> logger)
        {
            _parkingService = parkingService;
            _logger = logger;
        }

        /// <summary>
        /// Record a vehicle entry
        /// </summary>
        [HttpPost("entry")]
        public async Task<ActionResult<VehicleDto>> RecordEntry([FromBody] CreateVehicleEntryDto entryDto)
        {
            try
            {
                _logger.LogInformation("Recording vehicle entry for license plate: {LicensePlate}", entryDto.LicensePlate);
                
                // Check if vehicle is already parked
                if (await _parkingService.IsVehicleCurrentlyParkedAsync(entryDto.LicensePlate))
                {
                    return BadRequest($"Vehicle with license plate {entryDto.LicensePlate} is already parked");
                }
                
                // Check capacity
                if (!await _parkingService.IsCapacityAvailableAsync())
                {
                    return BadRequest("Parking lot is at full capacity");
                }
                
                var vehicle = await _parkingService.RecordVehicleEntryAsync(entryDto);
                return Ok(vehicle);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording vehicle entry for {LicensePlate}", entryDto.LicensePlate);
                return StatusCode(500, "An error occurred while recording vehicle entry");
            }
        }

        /// <summary>
        /// Record a vehicle exit
        /// </summary>
        [HttpPost("exit")]
        public async Task<ActionResult<ParkingTransactionDto>> RecordExit([FromBody] VehicleExitDto exitDto)
        {
            try
            {
                _logger.LogInformation("Recording vehicle exit for license plate: {LicensePlate}", exitDto.LicensePlate);
                
                var transaction = await _parkingService.RecordVehicleExitAsync(exitDto);
                return Ok(transaction);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid vehicle exit request for {LicensePlate}", exitDto.LicensePlate);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording vehicle exit for {LicensePlate}", exitDto.LicensePlate);
                return StatusCode(500, "An error occurred while recording vehicle exit");
            }
        }

        /// <summary>
        /// Get current vehicles in parking
        /// </summary>
        [HttpGet("current")]
        public async Task<ActionResult<VehicleListResponseDto>> GetCurrentVehicles([FromQuery] VehicleSearchDto searchDto)
        {
            try
            {
                searchDto.Status = VehicleStatus.Parked; // Only get currently parked vehicles
                var vehicles = await _parkingService.GetCurrentVehiclesAsync(searchDto);
                return Ok(vehicles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current vehicles");
                return StatusCode(500, "An error occurred while retrieving current vehicles");
            }
        }

        /// <summary>
        /// Search for a vehicle by license plate
        /// </summary>
        [HttpGet("search")]
        public async Task<ActionResult<VehicleDto>> SearchVehicle([FromQuery] string plate)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(plate))
                {
                    return BadRequest("License plate is required");
                }

                var vehicle = await _parkingService.GetVehicleByLicensePlateAsync(plate);
                if (vehicle == null)
                {
                    return NotFound($"Vehicle with license plate {plate} not found");
                }

                return Ok(vehicle);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching for vehicle with plate {Plate}", plate);
                return StatusCode(500, "An error occurred while searching for vehicle");
            }
        }

        /// <summary>
        /// Process payment for a transaction
        /// </summary>
        [HttpPost("payment")]
        public async Task<ActionResult<PaymentResponseDto>> ProcessPayment([FromBody] ProcessPaymentDto paymentDto)
        {
            try
            {
                _logger.LogInformation("Processing payment for transaction: {TransactionId}", paymentDto.TransactionId);
                
                var result = await _parkingService.ProcessPaymentAsync(paymentDto);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid payment request for transaction {TransactionId}", paymentDto.TransactionId);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for transaction {TransactionId}", paymentDto.TransactionId);
                return StatusCode(500, "An error occurred while processing payment");
            }
        }

        /// <summary>
        /// Calculate parking fee for a vehicle
        /// </summary>
        [HttpGet("calculate-fee/{vehicleId}")]
        public async Task<ActionResult<FeeCalculationDto>> CalculateFee(string vehicleId)
        {
            try
            {
                var calculation = await _parkingService.CalculateParkingFeeAsync(vehicleId);
                return Ok(calculation);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid fee calculation request for vehicle {VehicleId}", vehicleId);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating fee for vehicle {VehicleId}", vehicleId);
                return StatusCode(500, "An error occurred while calculating parking fee");
            }
        }

        /// <summary>
        /// Get recent transactions
        /// </summary>
        [HttpGet("transactions")]
        public async Task<ActionResult<TransactionListResponseDto>> GetTransactions([FromQuery] TransactionSearchDto searchDto)
        {
            try
            {
                var transactions = await _parkingService.GetTransactionsAsync(searchDto);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving transactions");
                return StatusCode(500, "An error occurred while retrieving transactions");
            }
        }

        /// <summary>
        /// Get pending transactions
        /// </summary>
        [HttpGet("transactions/pending")]
        public async Task<ActionResult<TransactionListResponseDto>> GetPendingTransactions()
        {
            try
            {
                var transactions = await _parkingService.GetPendingTransactionsAsync();
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pending transactions");
                return StatusCode(500, "An error occurred while retrieving pending transactions");
            }
        }

        /// <summary>
        /// Get today's statistics
        /// </summary>
        [HttpGet("stats/today")]
        public async Task<ActionResult<SystemStatistics>> GetTodayStats()
        {
            try
            {
                var stats = await _parkingService.GetTodayStatisticsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving today's statistics");
                return StatusCode(500, "An error occurred while retrieving statistics");
            }
        }

        /// <summary>
        /// Get historical statistics
        /// </summary>
        [HttpGet("stats/historical")]
        public async Task<ActionResult<List<SystemStatistics>>> GetHistoricalStats(
            [FromQuery] DateTime start, 
            [FromQuery] DateTime end)
        {
            try
            {
                if (start > end)
                {
                    return BadRequest("Start date must be before end date");
                }

                var stats = await _parkingService.GetStatisticsForPeriodAsync(start, end);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving historical statistics");
                return StatusCode(500, "An error occurred while retrieving historical statistics");
            }
        }

        /// <summary>
        /// Get parking configuration
        /// </summary>
        [HttpGet("config")]
        public async Task<ActionResult<ParkingConfiguration>> GetConfiguration()
        {
            try
            {
                var config = await _parkingService.GetConfigurationAsync();
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving parking configuration");
                return StatusCode(500, "An error occurred while retrieving configuration");
            }
        }

        /// <summary>
        /// Update parking configuration
        /// </summary>
        [HttpPut("config")]
        public async Task<ActionResult<ParkingConfiguration>> UpdateConfiguration([FromBody] ParkingConfiguration config)
        {
            try
            {
                var updatedConfig = await _parkingService.UpdateConfigurationAsync(config);
                return Ok(updatedConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating parking configuration");
                return StatusCode(500, "An error occurred while updating configuration");
            }
        }

        /// <summary>
        /// Generate report
        /// </summary>
        [HttpGet("reports/{type}")]
        public async Task<ActionResult<Dictionary<string, object>>> GenerateReport(string type, [FromQuery] DateTime date)
        {
            try
            {
                var report = await _parkingService.GenerateReportAsync(type, date);
                return Ok(report);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating {Type} report for {Date}", type, date);
                return StatusCode(500, "An error occurred while generating report");
            }
        }
    }
}
