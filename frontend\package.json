{"name": "parking-booth-frontend", "version": "1.0.0", "description": "HONGRUI 停车收费系统桌面客户端", "main": "electron/main.js", "homepage": "./", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "electron": "electron .", "electron:start": "node start-electron.js", "electron:smart": "node start-electron-dev.js", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && electron .\"", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "axios": "^1.11.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "concurrently": "^9.2.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10", "wait-on": "^8.0.4"}, "build": {"appId": "com.HONGRUI.parking-system", "productName": "HONGRUI停车收费系统", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "electron/assets", "to": "assets"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "electron/assets/icon.ico", "requestedExecutionLevel": "requireAdministrator"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "electron/assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "electron/assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "HONGRUI停车收费系统"}}}