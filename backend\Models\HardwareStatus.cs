using System.ComponentModel.DataAnnotations;

namespace ParkingBoothApi.Models
{
    public class HardwareStatus
    {
        public PrinterStatus Printer { get; set; } = new();
        public CameraStatus Camera { get; set; } = new();
        public CardReaderStatus CardReader { get; set; } = new();
        public GroundSensorStatus GroundSensor { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }
    
    public class PrinterStatus
    {
        public bool Connected { get; set; }
        public DeviceStatus Status { get; set; } = DeviceStatus.Offline;
        public int PaperLevel { get; set; } // Percentage 0-100
        public string? LastError { get; set; }
        public DateTime? LastPrintTime { get; set; }
    }
    
    public class CameraStatus
    {
        public bool Connected { get; set; }
        public DeviceStatus Status { get; set; } = DeviceStatus.Offline;
        public string Resolution { get; set; } = "1920x1080";
        public DateTime? LastCapture { get; set; }
        public string? LastImagePath { get; set; }
    }
    
    public class CardReaderStatus
    {
        public bool Connected { get; set; }
        public DeviceStatus Status { get; set; } = DeviceStatus.Offline;
        public DateTime? LastTransaction { get; set; }
        public decimal? LastCardBalance { get; set; }
        public string? LastCardId { get; set; }
    }
    
    public class GroundSensorStatus
    {
        public bool Connected { get; set; }
        public DeviceStatus Status { get; set; } = DeviceStatus.Offline;
        public bool VehicleDetected { get; set; }
        public DateTime? LastDetection { get; set; }
        public int Sensitivity { get; set; } = 5; // 1-10 scale
    }
    
    public enum DeviceStatus
    {
        Offline = 0,
        Ready = 1,
        Busy = 2,
        Error = 3,
        Capturing = 4,
        Reading = 5,
        Detecting = 6
    }
    
    public class HardwareCommand
    {
        [Required]
        public string DeviceType { get; set; } = string.Empty; // printer, camera, cardreader, groundsensor
        
        [Required]
        public string Command { get; set; } = string.Empty; // print, capture, read, detect, reset, etc.
        
        public Dictionary<string, object>? Parameters { get; set; }
        
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
    
    public class HardwareResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public Dictionary<string, object>? Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
