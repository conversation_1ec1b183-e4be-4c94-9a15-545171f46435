<template>
  <div class="api-test-container">
    <div class="test-header">
      <h2>API 连接测试</h2>
      <p>测试与后端API的连接状态</p>
    </div>

    <div class="test-sections">
      <!-- 基础连接测试 -->
      <div class="test-section">
        <h3>基础连接测试</h3>
        <div class="test-item">
          <button @click="testConnection" :disabled="loading.connection" class="test-btn">
            {{ loading.connection ? '测试中...' : '测试连接' }}
          </button>
          <span class="test-result" :class="results.connection.status">
            {{ results.connection.message }}
          </span>
        </div>
      </div>

      <!-- 认证测试 -->
      <div class="test-section">
        <h3>认证功能测试</h3>
        <div class="test-form">
          <div class="form-group">
            <label>用户名:</label>
            <input v-model="testCredentials.username" type="text" placeholder="输入测试用户名" />
          </div>
          <div class="form-group">
            <label>密码:</label>
            <input v-model="testCredentials.password" type="password" placeholder="输入测试密码" />
          </div>
          <button @click="testLogin" :disabled="loading.login" class="test-btn">
            {{ loading.login ? '登录中...' : '测试登录' }}
          </button>
          <span class="test-result" :class="results.login.status">
            {{ results.login.message }}
          </span>
        </div>
      </div>

      <!-- 数据获取测试 -->
      <div class="test-section">
        <h3>数据获取测试</h3>
        <div class="test-item">
          <button @click="testHomeStats" :disabled="loading.stats" class="test-btn">
            {{ loading.stats ? '获取中...' : '获取首页统计' }}
          </button>
          <span class="test-result" :class="results.stats.status">
            {{ results.stats.message }}
          </span>
        </div>
        
        <div class="test-item">
          <button @click="testVehicleRecords" :disabled="loading.vehicles" class="test-btn">
            {{ loading.vehicles ? '获取中...' : '获取车辆记录' }}
          </button>
          <span class="test-result" :class="results.vehicles.status">
            {{ results.vehicles.message }}
          </span>
        </div>
      </div>

      <!-- API配置信息 -->
      <div class="test-section">
        <h3>API配置信息</h3>
        <div class="config-info">
          <div class="config-item">
            <strong>API地址:</strong> {{ apiConfig.baseUrl }}
          </div>
          <div class="config-item">
            <strong>超时时间:</strong> {{ apiConfig.timeout }}ms
          </div>
          <div class="config-item">
            <strong>环境:</strong> {{ apiConfig.environment }}
          </div>
        </div>
      </div>

      <!-- 测试结果日志 -->
      <div class="test-section">
        <h3>测试日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import httpClient from '@/services/httpClient'
import authApi from '@/services/authApi'
import parkingApi from '@/services/parkingApi'
import { getApiConfig } from '@/config/api'

// 加载状态
const loading = reactive({
  connection: false,
  login: false,
  stats: false,
  vehicles: false
})

// 测试结果
const results = reactive({
  connection: { status: '', message: '未测试' },
  login: { status: '', message: '未测试' },
  stats: { status: '', message: '未测试' },
  vehicles: { status: '', message: '未测试' }
})

// 测试凭据
const testCredentials = reactive({
  username: 'admin',
  password: 'admin123'
})

// API配置
const apiConfig = reactive({
  baseUrl: '',
  timeout: 0,
  environment: ''
})

// 日志
const logs = ref<Array<{ time: string, message: string, type: string }>>([])

// 添加日志
const addLog = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, message, type })
  if (logs.value.length > 50) {
    logs.value.pop()
  }
}

// 测试基础连接
const testConnection = async () => {
  loading.connection = true
  addLog('开始测试基础连接...', 'info')
  
  try {
    // 尝试获取系统参数来测试连接
    const response = await parkingApi.getSystemParams()
    
    if (response.status === 1) {
      results.connection.status = 'success'
      results.connection.message = '连接成功'
      addLog('基础连接测试成功', 'success')
    } else {
      results.connection.status = 'warning'
      results.connection.message = '连接成功但响应异常'
      addLog(`连接响应异常: ${response.msg || '未知错误'}`, 'error')
    }
  } catch (error: any) {
    results.connection.status = 'error'
    results.connection.message = '连接失败'
    addLog(`连接失败: ${error.message}`, 'error')
  } finally {
    loading.connection = false
  }
}

// 测试登录
const testLogin = async () => {
  if (!testCredentials.username || !testCredentials.password) {
    addLog('请输入用户名和密码', 'error')
    return
  }

  loading.login = true
  addLog(`开始测试登录: ${testCredentials.username}`, 'info')
  
  try {
    const response = await authApi.directLogin(testCredentials)
    
    if (response.status === 1) {
      results.login.status = 'success'
      results.login.message = '登录成功'
      addLog('登录测试成功', 'success')
    } else {
      results.login.status = 'error'
      results.login.message = '登录失败'
      addLog(`登录失败: ${response.msg || '用户名或密码错误'}`, 'error')
    }
  } catch (error: any) {
    results.login.status = 'error'
    results.login.message = '登录异常'
    addLog(`登录异常: ${error.message}`, 'error')
  } finally {
    loading.login = false
  }
}

// 测试首页统计
const testHomeStats = async () => {
  loading.stats = true
  addLog('开始测试首页统计数据获取...', 'info')
  
  try {
    const response = await parkingApi.getHomeStats()
    
    if (response.status === 1) {
      results.stats.status = 'success'
      results.stats.message = '数据获取成功'
      addLog(`统计数据获取成功: ${JSON.stringify(response.data)}`, 'success')
    } else {
      results.stats.status = 'warning'
      results.stats.message = '数据获取异常'
      addLog(`数据获取异常: ${response.msg || '未知错误'}`, 'error')
    }
  } catch (error: any) {
    results.stats.status = 'error'
    results.stats.message = '数据获取失败'
    addLog(`数据获取失败: ${error.message}`, 'error')
  } finally {
    loading.stats = false
  }
}

// 测试车辆记录
const testVehicleRecords = async () => {
  loading.vehicles = true
  addLog('开始测试车辆记录获取...', 'info')
  
  try {
    const response = await parkingApi.getVehicleRecords({
      pageNum: 1,
      pageSize: 10
    })
    
    if (response.status === 1) {
      results.vehicles.status = 'success'
      results.vehicles.message = `获取成功 (${response.data?.total || 0}条记录)`
      addLog(`车辆记录获取成功: ${response.data?.total || 0}条记录`, 'success')
    } else {
      results.vehicles.status = 'warning'
      results.vehicles.message = '记录获取异常'
      addLog(`记录获取异常: ${response.msg || '未知错误'}`, 'error')
    }
  } catch (error: any) {
    results.vehicles.status = 'error'
    results.vehicles.message = '记录获取失败'
    addLog(`记录获取失败: ${error.message}`, 'error')
  } finally {
    loading.vehicles = false
  }
}

// 初始化
onMounted(() => {
  const config = getApiConfig()
  apiConfig.baseUrl = config.BASE_URL
  apiConfig.timeout = config.TIMEOUT
  apiConfig.environment = process.env.NODE_ENV || 'development'
  
  addLog('API测试页面已加载', 'info')
})
</script>

<style scoped>
.api-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-sections {
  display: grid;
  gap: 20px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: #34495e;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-group label {
  min-width: 60px;
  font-weight: 500;
}

.form-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
  max-width: 200px;
}

.test-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
  min-width: 120px;
}

.test-btn:hover:not(:disabled) {
  background: #2980b9;
}

.test-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.test-result {
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 4px;
}

.test-result.success {
  color: #27ae60;
  background: #d5f4e6;
}

.test-result.error {
  color: #e74c3c;
  background: #fdf2f2;
}

.test-result.warning {
  color: #f39c12;
  background: #fef9e7;
}

.config-info {
  display: grid;
  gap: 10px;
}

.config-item {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 14px;
}

.log-time {
  color: #7f8c8d;
  min-width: 80px;
}

.log-item.success .log-message {
  color: #27ae60;
}

.log-item.error .log-message {
  color: #e74c3c;
}

.log-item.info .log-message {
  color: #34495e;
}
</style>
