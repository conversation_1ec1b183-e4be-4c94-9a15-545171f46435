import parkingApi from './parkingApi'
import type { HomeStats, VehicleInPark, VehicleRecord, ChargeDetail } from './parkingApi'

// 兼容原有接口的类型定义
export interface Vehicle {
  id: string
  licensePlate: string
  vehicleType: string
  entryTime: Date
  exitTime?: Date
}

export interface ParkingTransaction {
  id: string
  vehicleId: string
  amount: number
  paymentMethod: 'cash' | 'card' | 'qr'
  createdAt: Date
}

export interface ParkingStats {
  totalVehicles: number
  currentOccupancy: number
  totalRevenue: number
  averageStayTime: number
  peakHours: string[]
}

export class ParkingService {
  // 获取今日统计数据
  async getTodayStats(): Promise<ParkingStats> {
    try {
      // 调用真实API获取首页统计数据
      const response = await parkingApi.getHomeStats()

      if (response.status === 1 && response.data) {
        const data = response.data
        return {
          totalVehicles: data.totalVehicles || 0,
          currentOccupancy: data.currentOccupancy || 0,
          totalRevenue: data.totalAmount || 0,
          averageStayTime: 120, // 默认值，如果API没有提供
          peakHours: ['09:00-10:00', '17:00-18:00'] // 默认值
        }
      }

      // 如果API调用失败，返回模拟数据
      return this.getMockStats()
    } catch (error) {
      console.error('Failed to get today stats from API, using mock data:', error)
      return this.getMockStats()
    }
  }

  // 获取当前车辆列表
  async getCurrentVehicles(): Promise<Vehicle[]> {
    try {
      // 调用真实API获取场内车辆
      const response = await parkingApi.getVehiclesInPark({
        pageNum: 1,
        pageSize: 100
      })

      if (response.status === 1 && response.data?.list) {
        return response.data.list.map((vehicle: VehicleInPark) => ({
          id: vehicle.id,
          licensePlate: vehicle.plateNumber,
          vehicleType: vehicle.vehicleType,
          entryTime: new Date(vehicle.entryTime)
        }))
      }

      // 如果API调用失败，返回模拟数据
      return this.getMockVehicles()
    } catch (error) {
      console.error('Failed to get current vehicles from API, using mock data:', error)
      return this.getMockVehicles()
    }
  }

  // 获取车辆进出记录
  async getVehicleRecords(params: {
    startDate?: string
    endDate?: string
    plateNumber?: string
    pageNum?: number
    pageSize?: number
  }): Promise<{ list: VehicleRecord[], total: number }> {
    try {
      const response = await parkingApi.getVehicleRecords(params)

      if (response.status === 1 && response.data) {
        return response.data
      }

      return { list: [], total: 0 }
    } catch (error) {
      console.error('Failed to get vehicle records:', error)
      return { list: [], total: 0 }
    }
  }

  // 获取收费明细
  async getChargeDetails(params: {
    startDate?: string
    endDate?: string
    plateNumber?: string
    pageNum?: number
    pageSize?: number
  }): Promise<{ list: ChargeDetail[], total: number }> {
    try {
      const response = await parkingApi.getChargeDetails(params)

      if (response.status === 1 && response.data) {
        return response.data
      }

      return { list: [], total: 0 }
    } catch (error) {
      console.error('Failed to get charge details:', error)
      return { list: [], total: 0 }
    }
  }

  // 控制闸机
  async controlGate(channelMac: string, action: 'open' | 'close' | 'capture'): Promise<boolean> {
    try {
      const actionCode = action === 'open' ? 0 : action === 'close' ? 3 : 1
      const response = await parkingApi.controlGate(channelMac, actionCode)

      return response.status === 1
    } catch (error) {
      console.error('Failed to control gate:', error)
      return false
    }
  }

  // 车牌修改
  async updatePlateNumber(carNumUid: string, newPlateNumber: string, reason?: string): Promise<boolean> {
    try {
      const response = await parkingApi.updatePlateNumber({
        carNumUid,
        newPlateNumber,
        reason
      })

      return response.status === 1
    } catch (error) {
      console.error('Failed to update plate number:', error)
      return false
    }
  }

  // 免费放行
  async freePass(plateNumber: string, reason: string, operator: string): Promise<boolean> {
    try {
      const response = await parkingApi.freePass({
        plateNumber,
        reason,
        operator
      })

      return response.status === 1
    } catch (error) {
      console.error('Failed to free pass:', error)
      return false
    }
  }

  // 模拟数据方法（作为备用）
  private getMockStats(): ParkingStats {
    return {
      totalVehicles: Math.floor(Math.random() * 50) + 100,
      currentOccupancy: Math.floor(Math.random() * 20) + 30,
      totalRevenue: Math.floor(Math.random() * 500) + 1000,
      averageStayTime: Math.floor(Math.random() * 60) + 120,
      peakHours: ['09:00-10:00', '17:00-18:00']
    }
  }

  private getMockVehicles(): Vehicle[] {
    return [
      {
        id: '1',
        licensePlate: '粤B12345',
        vehicleType: '小型车',
        entryTime: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
      },
      {
        id: '2',
        licensePlate: '粤B67890',
        vehicleType: 'SUV',
        entryTime: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
      }
    ]
  }
}
