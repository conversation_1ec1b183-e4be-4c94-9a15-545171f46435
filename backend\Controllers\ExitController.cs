using Microsoft.AspNetCore.Mvc;
using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ExitController : ControllerBase
    {
        private readonly IExitService _exitService;
        private readonly ILogger<ExitController> _logger;

        public ExitController(IExitService exitService, ILogger<ExitController> logger)
        {
            _exitService = exitService;
            _logger = logger;
        }

        /// <summary>
        /// 车辆出口检测 - 地感触发时调用
        /// </summary>
        [HttpPost("detect")]
        public async Task<ActionResult<ExitVehicleDetectionDto>> DetectVehicle([FromBody] ExitVehicleDetectionDto detection)
        {
            try
            {
                var result = await _exitService.ProcessVehicleDetectionAsync(detection);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "车辆出口检测失败");
                return StatusCode(500, new { message = "车辆出口检测失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 现金收费
        /// </summary>
        [HttpPost("payment/cash")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessCashPayment([FromBody] CashPaymentRequestDto request)
        {
            try
            {
                var paymentDto = new PaymentProcessDto
                {
                    DetectionId = request.DetectionId,
                    PaymentMethod = PaymentMethod.Cash,
                    Amount = request.Amount,
                    OperatorId = request.OperatorId,
                    Remarks = request.Remarks
                };

                var result = await _exitService.ProcessPaymentAsync(paymentDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "现金支付处理失败");
                return StatusCode(500, new { message = "现金支付处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 扫码收费
        /// </summary>
        [HttpPost("payment/qr")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessQrPayment([FromBody] QrPaymentRequestDto request)
        {
            try
            {
                var paymentDto = new PaymentProcessDto
                {
                    DetectionId = request.DetectionId,
                    PaymentMethod = PaymentMethod.QrCode,
                    Amount = request.Amount,
                    QrCodeData = request.QrCodeData,
                    OperatorId = request.OperatorId
                };

                var result = await _exitService.ProcessPaymentAsync(paymentDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫码支付处理失败");
                return StatusCode(500, new { message = "扫码支付处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 抵用券支付
        /// </summary>
        [HttpPost("payment/coupon")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessCouponPayment([FromBody] CouponPaymentRequestDto request)
        {
            try
            {
                var paymentDto = new PaymentProcessDto
                {
                    DetectionId = request.DetectionId,
                    PaymentMethod = PaymentMethod.Coupon,
                    Amount = request.Amount,
                    UsedCoupons = request.UsedCoupons,
                    OperatorId = request.OperatorId
                };

                var result = await _exitService.ProcessPaymentAsync(paymentDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "抵用券支付处理失败");
                return StatusCode(500, new { message = "抵用券支付处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 免费放行
        /// </summary>
        [HttpPost("payment/free")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessFreeExit([FromBody] FreeExitRequestDto request)
        {
            try
            {
                var paymentDto = new PaymentProcessDto
                {
                    DetectionId = request.DetectionId,
                    PaymentMethod = PaymentMethod.Free,
                    Amount = 0,
                    OperatorId = request.OperatorId,
                    Remarks = request.Reason
                };

                var result = await _exitService.ProcessPaymentAsync(paymentDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "免费放行处理失败");
                return StatusCode(500, new { message = "免费放行处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 验票出场
        /// </summary>
        [HttpPost("ticket-exit")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessTicketExit([FromBody] TicketExitDto ticketDto)
        {
            try
            {
                var result = await _exitService.ProcessTicketExitAsync(ticketDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验票出场处理失败");
                return StatusCode(500, new { message = "验票出场处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 刷卡出场
        /// </summary>
        [HttpPost("card-exit")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessCardExit([FromBody] CardExitDto cardDto)
        {
            try
            {
                var result = await _exitService.ProcessCardExitAsync(cardDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷卡出场处理失败");
                return StatusCode(500, new { message = "刷卡出场处理失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 强制放行
        /// </summary>
        [HttpPost("force-exit")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessForceExit([FromBody] ForceExitRequestDto request)
        {
            try
            {
                var actionDto = new ManualExitActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = ExitAction.ForceExit,
                    OperatorId = request.OperatorId,
                    Reason = request.Reason
                };

                var result = await _exitService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "强制放行失败");
                return StatusCode(500, new { message = "强制放行失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 手动补录
        /// </summary>
        [HttpPost("manual-entry")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessManualEntry([FromBody] ManualEntryRequestDto request)
        {
            try
            {
                var actionDto = new ManualExitActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = ExitAction.ManualEntry,
                    ModifiedLicensePlate = request.LicensePlate,
                    ModifiedVehicleType = request.VehicleType,
                    ManualEntryTime = request.EntryTime,
                    OperatorId = request.OperatorId,
                    Reason = "手动补录"
                };

                var result = await _exitService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动补录失败");
                return StatusCode(500, new { message = "手动补录失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 入场匹配
        /// </summary>
        [HttpPost("manual-match")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessManualMatch([FromBody] ManualMatchRequestDto request)
        {
            try
            {
                var actionDto = new ManualExitActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = ExitAction.ManualMatch,
                    SelectedVehicleId = request.SelectedVehicleId,
                    OperatorId = request.OperatorId,
                    Reason = "手动匹配"
                };

                var result = await _exitService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动匹配失败");
                return StatusCode(500, new { message = "手动匹配失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 重新抓拍
        /// </summary>
        [HttpPost("recapture")]
        public async Task<ActionResult<ExitProcessResultDto>> ProcessRecapture([FromBody] RecaptureRequestDto request)
        {
            try
            {
                var actionDto = new ManualExitActionDto
                {
                    DetectionId = request.DetectionId,
                    Action = ExitAction.Recapture,
                    OperatorId = request.OperatorId,
                    Reason = request.Reason
                };

                var result = await _exitService.ProcessManualActionAsync(actionDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新抓拍失败");
                return StatusCode(500, new { message = "重新抓拍失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 道闸控制
        /// </summary>
        [HttpPost("barrier-control")]
        public async Task<ActionResult<bool>> ControlBarrier([FromBody] ExitBarrierControlDto controlDto)
        {
            try
            {
                var result = await _exitService.ControlExitBarrierAsync(
                    controlDto.ExitId, 
                    controlDto.Action, 
                    controlDto.OperatorId, 
                    controlDto.Reason);
                
                return Ok(new { success = result, message = result ? "道闸控制成功" : "道闸控制失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "道闸控制失败");
                return StatusCode(500, new { message = "道闸控制失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取出口状态
        /// </summary>
        [HttpGet("status")]
        public async Task<ActionResult<List<ExitStatusDto>>> GetExitStatus()
        {
            try
            {
                var status = await _exitService.GetExitStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取出口状态失败");
                return StatusCode(500, new { message = "获取出口状态失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取班次统计
        /// </summary>
        [HttpGet("shift-summary/{operatorId}")]
        public async Task<ActionResult<ShiftSummaryDto>> GetShiftSummary(string operatorId)
        {
            try
            {
                var summary = await _exitService.GetCurrentShiftSummaryAsync(operatorId);
                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取班次统计失败");
                return StatusCode(500, new { message = "获取班次统计失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取车位信息
        /// </summary>
        [HttpGet("parking-spaces")]
        public async Task<ActionResult<ParkingSpaceInfoDto>> GetParkingSpaces()
        {
            try
            {
                var spaces = await _exitService.GetParkingSpaceInfoAsync();
                return Ok(spaces);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取车位信息失败");
                return StatusCode(500, new { message = "获取车位信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取可用抵用券
        /// </summary>
        [HttpGet("coupons")]
        public async Task<ActionResult<List<CouponDto>>> GetAvailableCoupons()
        {
            try
            {
                var coupons = await _exitService.GetAvailableCouponsAsync();
                return Ok(coupons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取抵用券失败");
                return StatusCode(500, new { message = "获取抵用券失败", error = ex.Message });
            }
        }
    }

    // 请求 DTO 类
    public class CashPaymentRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string OperatorId { get; set; } = string.Empty;
        public string? Remarks { get; set; }
    }

    public class QrPaymentRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string QrCodeData { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
    }

    public class CouponPaymentRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public List<CouponDto> UsedCoupons { get; set; } = new();
        public string OperatorId { get; set; } = string.Empty;
    }

    public class FreeExitRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class ForceExitRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class ManualEntryRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string LicensePlate { get; set; } = string.Empty;
        public VehicleType VehicleType { get; set; }
        public DateTime EntryTime { get; set; }
        public string OperatorId { get; set; } = string.Empty;
    }

    public class ManualMatchRequestDto
    {
        public string DetectionId { get; set; } = string.Empty;
        public string SelectedVehicleId { get; set; } = string.Empty;
        public string OperatorId { get; set; } = string.Empty;
    }

    public class ExitBarrierControlDto
    {
        public string ExitId { get; set; } = string.Empty;
        public BarrierAction Action { get; set; }
        public string OperatorId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }
}
