# Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Parking Booth Management System in production environments. The system consists of a Vue.js frontend and .NET backend, both designed to run on the same machine.

## System Requirements

### Minimum Hardware Requirements
- **CPU**: Intel Core i5 or AMD Ryzen 5 (4 cores)
- **RAM**: 8GB DDR4
- **Storage**: 256GB SSD
- **Network**: Gigabit Ethernet
- **USB Ports**: 4+ for hardware devices
- **Display**: 1920x1080 touchscreen (optional)

### Recommended Hardware Requirements
- **CPU**: Intel Core i7 or AMD Ryzen 7 (8 cores)
- **RAM**: 16GB DDR4
- **Storage**: 512GB NVMe SSD
- **Network**: Gigabit Ethernet + Wi-Fi
- **USB Ports**: 6+ for hardware devices
- **Display**: 1920x1080 touchscreen

### Operating System Support
- **Windows**: Windows 10/11 Pro (64-bit)
- **Linux**: Ubuntu 20.04+ LTS, CentOS 8+
- **Hardware**: Industrial PC or embedded system

## Software Prerequisites

### Windows Deployment
```powershell
# Install .NET 9 Runtime
winget install Microsoft.DotNet.Runtime.9

# Install Node.js
winget install OpenJS.NodeJS

# Install IIS (optional)
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole
```

### Linux Deployment
```bash
# Install .NET 9 Runtime
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y dotnet-runtime-9.0

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Nginx
sudo apt-get install -y nginx
```

## Build Process

### Frontend Build
```bash
cd frontend
npm install
npm run build
```

### Backend Build
```bash
cd backend
dotnet publish -c Release -o ./publish
```

## Deployment Options

### Option 1: Self-Contained Deployment (Recommended)

**Advantages**:
- No runtime dependencies
- Isolated environment
- Easy deployment

**Build Commands**:
```bash
# Backend
cd backend
dotnet publish -c Release -r win-x64 --self-contained true -o ./publish

# Frontend (already built above)
cd frontend
npm run build
```

### Option 2: Framework-Dependent Deployment

**Advantages**:
- Smaller deployment size
- Shared runtime updates

**Requirements**:
- .NET 9 runtime installed on target machine

### Option 3: Docker Deployment

**Dockerfile for Backend**:
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["ParkingBoothApi.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ParkingBoothApi.dll"]
```

**Dockerfile for Frontend**:
```dockerfile
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

## Configuration

### Production Configuration Files

**Backend (appsettings.Production.json)**:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Urls": "http://localhost:5000",
  "ParkingBooth": {
    "BoothId": "001",
    "MaxCapacity": 100,
    "Environment": "Production"
  },
  "Hardware": {
    "EnableSimulation": false,
    "ConnectionTimeout": 5000,
    "RetryAttempts": 3
  }
}
```

**Frontend (.env.production)**:
```env
VITE_API_BASE_URL=http://localhost:5000/api
VITE_BOOTH_ID=001
VITE_ENVIRONMENT=production
VITE_LOG_LEVEL=warn
```

### Nginx Configuration (Linux)**:
```nginx
server {
    listen 80;
    server_name localhost;
    
    # Frontend
    location / {
        root /var/www/parking-booth/frontend;
        try_files $uri $uri/ /index.html;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:5000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Installation Steps

### Windows Installation

1. **Prepare Deployment Directory**:
```powershell
mkdir C:\ParkingBooth
cd C:\ParkingBooth
```

2. **Deploy Backend**:
```powershell
# Copy published backend files
xcopy /E /I backend\publish backend

# Create Windows Service (optional)
sc create ParkingBoothAPI binPath="C:\ParkingBooth\backend\ParkingBoothApi.exe"
sc start ParkingBoothAPI
```

3. **Deploy Frontend**:
```powershell
# Copy built frontend files
xcopy /E /I frontend\dist frontend

# Configure IIS (if using)
Import-Module WebAdministration
New-Website -Name "ParkingBooth" -Port 80 -PhysicalPath "C:\ParkingBooth\frontend"
```

### Linux Installation

1. **Prepare Deployment Directory**:
```bash
sudo mkdir -p /opt/parking-booth
sudo chown $USER:$USER /opt/parking-booth
cd /opt/parking-booth
```

2. **Deploy Backend**:
```bash
# Copy published backend files
cp -r backend/publish backend

# Create systemd service
sudo tee /etc/systemd/system/parking-booth-api.service > /dev/null <<EOF
[Unit]
Description=Parking Booth API
After=network.target

[Service]
Type=notify
ExecStart=/opt/parking-booth/backend/ParkingBoothApi
Restart=always
RestartSec=5
User=www-data
Environment=ASPNETCORE_ENVIRONMENT=Production

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable parking-booth-api
sudo systemctl start parking-booth-api
```

3. **Deploy Frontend**:
```bash
# Copy built frontend files
sudo cp -r frontend/dist/* /var/www/parking-booth/frontend/

# Configure Nginx
sudo cp nginx.conf /etc/nginx/sites-available/parking-booth
sudo ln -s /etc/nginx/sites-available/parking-booth /etc/nginx/sites-enabled/
sudo systemctl reload nginx
```

## Hardware Setup

### Device Configuration
1. **Connect Hardware Devices**:
   - Thermal printer via USB
   - IP camera via Ethernet
   - Card reader via USB
   - Ground sensor via serial/USB

2. **Install Device Drivers**:
   - Follow manufacturer instructions
   - Test device connectivity
   - Configure device settings

3. **Update Configuration**:
   - Update hardware settings in appsettings.json
   - Test hardware integration
   - Verify all devices are detected

## Security Configuration

### Firewall Rules
```bash
# Linux (ufw)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5000/tcp  # Backend API (if exposed)

# Windows (PowerShell)
New-NetFirewallRule -DisplayName "Parking Booth HTTP" -Direction Inbound -Protocol TCP -LocalPort 80
New-NetFirewallRule -DisplayName "Parking Booth HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443
```

### SSL/TLS Configuration
```bash
# Generate self-signed certificate (development)
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Configure HTTPS in appsettings.json
{
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://localhost:5001",
        "Certificate": {
          "Path": "cert.pem",
          "KeyPath": "key.pem"
        }
      }
    }
  }
}
```

## Monitoring and Logging

### Log Configuration
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"],
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "logs/parking-booth-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  }
}
```

### Health Monitoring
```bash
# Check service status
systemctl status parking-booth-api

# Check logs
journalctl -u parking-booth-api -f

# Check API health
curl http://localhost:5000/api/health
```

## Backup and Recovery

### Backup Strategy
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/parking-booth/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup configuration
cp -r /opt/parking-booth/backend/appsettings*.json $BACKUP_DIR/
cp -r /var/www/parking-booth/frontend/.env* $BACKUP_DIR/

# Backup logs
cp -r /opt/parking-booth/logs $BACKUP_DIR/

# Backup data (if using file-based storage)
cp -r /opt/parking-booth/data $BACKUP_DIR/

echo "Backup completed: $BACKUP_DIR"
```

### Recovery Procedure
1. Stop services
2. Restore configuration files
3. Restore data files
4. Restart services
5. Verify functionality

## Maintenance

### Regular Maintenance Tasks
- **Daily**: Check service status, review logs
- **Weekly**: Update system packages, check disk space
- **Monthly**: Review performance metrics, update certificates
- **Quarterly**: Full system backup, security audit

### Update Procedure
```bash
# Stop services
sudo systemctl stop parking-booth-api
sudo systemctl stop nginx

# Backup current installation
cp -r /opt/parking-booth /backup/parking-booth-$(date +%Y%m%d)

# Deploy new version
# ... deployment steps ...

# Start services
sudo systemctl start parking-booth-api
sudo systemctl start nginx

# Verify functionality
curl http://localhost:5000/api/health
```

## Troubleshooting

### Common Issues

**Service Won't Start**:
- Check configuration files
- Verify file permissions
- Review system logs

**Hardware Not Detected**:
- Check device connections
- Verify drivers installed
- Test device independently

**Performance Issues**:
- Monitor system resources
- Check network connectivity
- Review application logs

### Support Contacts
- Technical Support: <EMAIL>
- Emergency Hotline: +1-800-PARKING
- Documentation: https://docs.parkingbooth.com
