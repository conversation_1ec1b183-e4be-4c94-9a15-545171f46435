using ParkingBoothApi.DTOs;
using ParkingBoothApi.Models;
using ParkingBoothApi.Interfaces;

namespace ParkingBoothApi.Services
{
    public class EntranceService : IEntranceService
    {
        private readonly ILogger<EntranceService> _logger;
        private readonly IParkingService _parkingService;
        private static readonly List<VehicleDetectionDto> _detections = new();
        private static readonly List<EntryNotificationDto> _notifications = new();
        private static readonly List<EntranceStatusDto> _entrances = new();
        private static readonly Dictionary<string, bool> _barrierStates = new();

        public EntranceService(ILogger<EntranceService> logger, IParkingService parkingService)
        {
            _logger = logger;
            _parkingService = parkingService;
            InitializeEntrances();
        }

        private void InitializeEntrances()
        {
            var entranceIds = new[] { "entrance-1", "entrance-2", "entrance-b2", "entrance-truck" };
            var entranceNames = new[] { "私家车入口1", "私家车入口2", "B2入口", "货车入口" };

            for (int i = 0; i < entranceIds.Length; i++)
            {
                _entrances.Add(new EntranceStatusDto
                {
                    EntranceId = entranceIds[i],
                    Name = entranceNames[i],
                    IsOnline = true,
                    IsBarrierOpen = false,
                    IsCameraWorking = true,
                    IsCardReaderWorking = true,
                    QueueLength = 0,
                    LastActivity = DateTime.UtcNow
                });
                _barrierStates[entranceIds[i]] = false;
            }
        }

        public async Task<VehicleDetectionDto> ProcessVehicleDetectionAsync(VehicleDetectionDto detection)
        {
            _logger.LogInformation("Processing vehicle detection: {LicensePlate} at {EntranceId}", 
                detection.LicensePlate, detection.EntranceId);

            detection.Id = Guid.NewGuid().ToString();
            detection.DetectedAt = DateTime.UtcNow;
            detection.Status = VehicleEntryStatus.Processing;

            _detections.Add(detection);

            // 更新入口状态
            var entrance = _entrances.FirstOrDefault(e => e.EntranceId == detection.EntranceId);
            if (entrance != null)
            {
                entrance.LastActivity = DateTime.UtcNow;
                entrance.PendingVehicles.Add(detection);
                entrance.QueueLength = entrance.PendingVehicles.Count;
            }

            // 自动决策是否允许进场
            var decision = await MakeEntryDecisionAsync(detection);
            
            if (decision.CanEnter && !decision.RequiresManualIntervention)
            {
                // 自动放行
                await ProcessAutomaticEntryAsync(detection, decision);
            }
            else
            {
                // 需要人工处理
                await CreateEntryNotificationAsync(detection, decision);
            }

            return detection;
        }

        public async Task<EntryDecisionDto> MakeEntryDecisionAsync(VehicleDetectionDto detection)
        {
            var decision = new EntryDecisionDto
            {
                DetectionId = detection.Id,
                ProcessedAt = DateTime.UtcNow
            };

            var blockingReasons = new List<string>();

            // 检查车位是否可用
            var availableSpots = await _parkingService.GetAvailableSpotsAsync();
            if (availableSpots <= 0)
            {
                blockingReasons.Add("车位已满");
            }

            // 检查车牌是否已在场内
            var isParked = await _parkingService.IsVehicleCurrentlyParkedAsync(detection.LicensePlate);
            if (isParked)
            {
                blockingReasons.Add("车辆已在场内");
            }

            // 检查车牌格式
            var isValidPlate = await _parkingService.IsValidLicensePlateAsync(detection.LicensePlate);
            if (!isValidPlate)
            {
                blockingReasons.Add("车牌号格式错误");
            }

            // 检查识别置信度
            if (detection.Confidence < 0.8)
            {
                blockingReasons.Add("车牌识别置信度过低");
                decision.RequiresManualIntervention = true;
            }

            // 模拟黑名单检查
            var blacklistedPlates = new[] { "黑名单001", "BLOCKED123" };
            if (blacklistedPlates.Contains(detection.LicensePlate))
            {
                blockingReasons.Add("车辆在黑名单中");
                decision.Category = VehicleCategory.Blacklisted;
            }

            // 模拟月卡车检查
            var monthlyPlates = new[] { "月卡001", "VIP123", "粤B12345" };
            if (monthlyPlates.Contains(detection.LicensePlate))
            {
                decision.Category = VehicleCategory.Monthly;
                decision.EstimatedFee = 0;
            }
            else
            {
                decision.Category = VehicleCategory.Temporary;
                decision.EstimatedFee = 5.0m; // 临时车预估费用
            }

            decision.CanEnter = blockingReasons.Count == 0;
            decision.BlockingReasons = blockingReasons;
            decision.Reason = decision.CanEnter ? "允许进场" : string.Join(", ", blockingReasons);

            return decision;
        }

        public async Task<EntryProcessResultDto> ProcessManualActionAsync(ManualEntryActionDto actionDto)
        {
            _logger.LogInformation("Processing manual action: {Action} for detection {DetectionId}", 
                actionDto.Action, actionDto.DetectionId);

            var detection = _detections.FirstOrDefault(d => d.Id == actionDto.DetectionId);
            if (detection == null)
            {
                return new EntryProcessResultDto
                {
                    Success = false,
                    Message = "未找到对应的车辆检测记录"
                };
            }

            var result = new EntryProcessResultDto { ProcessedAt = DateTime.UtcNow };

            switch (actionDto.Action)
            {
                case EntryAction.Allow:
                    result = await ProcessManualAllowAsync(detection, actionDto);
                    break;
                case EntryAction.Reject:
                    result = await ProcessRejectAsync(detection, actionDto);
                    break;
                case EntryAction.ManualRelease:
                    result = await ProcessManualReleaseAsync(detection, actionDto);
                    break;
                case EntryAction.ModifyInfo:
                    result = await ProcessModifyInfoAsync(detection, actionDto);
                    break;
                case EntryAction.Recapture:
                    result = await ProcessRecaptureAsync(detection, actionDto);
                    break;
                case EntryAction.Postpone:
                    result = await ProcessPostponeAsync(detection, actionDto);
                    break;
                case EntryAction.CloseBarrier:
                    result = await ProcessCloseBarrierAsync(detection, actionDto);
                    break;
                default:
                    result.Success = false;
                    result.Message = "不支持的操作类型";
                    break;
            }

            // 更新通知状态
            var notification = _notifications.FirstOrDefault(n => 
                n.VehicleInfo?.Id == actionDto.DetectionId && !n.IsHandled);
            if (notification != null)
            {
                notification.IsHandled = true;
                notification.HandledBy = actionDto.OperatorId;
                notification.HandledAt = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<EntryProcessResultDto> ProcessCardEntryAsync(CardEntryDto cardDto)
        {
            _logger.LogInformation("Processing card entry: {CardId} at {EntranceId}", 
                cardDto.CardId, cardDto.EntranceId);

            // 模拟卡片验证
            var validCards = new[] { "CARD001", "CARD002", "VIP123" };
            if (!validCards.Contains(cardDto.CardId))
            {
                return new EntryProcessResultDto
                {
                    Success = false,
                    Message = "无效的卡片"
                };
            }

            // 创建虚拟检测记录
            var detection = new VehicleDetectionDto
            {
                LicensePlate = cardDto.DetectedLicensePlate ?? "刷卡进场",
                VehicleType = VehicleType.Car,
                EntranceId = cardDto.EntranceId,
                Confidence = 1.0,
                Status = VehicleEntryStatus.Approved
            };

            // 直接允许进场
            var entryDto = new CreateVehicleEntryDto
            {
                LicensePlate = detection.LicensePlate,
                VehicleType = detection.VehicleType,
                ImageUrl = "",
                ParkingSpot = await GetAvailableParkingSpotAsync()
            };

            var vehicle = await _parkingService.RecordVehicleEntryAsync(entryDto);

            // 自动开闸
            await ControlBarrierAsync(new BarrierControlDto
            {
                EntranceId = cardDto.EntranceId,
                Action = BarrierAction.Open,
                OperatorId = "SYSTEM",
                Reason = "刷卡进场"
            });

            // 创建成功通知
            await CreateSuccessNotificationAsync(detection, "刷卡进场成功");

            return new EntryProcessResultDto
            {
                Success = true,
                Message = "刷卡进场成功",
                Vehicle = vehicle
            };
        }

        public async Task<bool> ControlBarrierAsync(BarrierControlDto controlDto)
        {
            _logger.LogInformation("Controlling barrier: {Action} at {EntranceId}", 
                controlDto.Action, controlDto.EntranceId);

            var entrance = _entrances.FirstOrDefault(e => e.EntranceId == controlDto.EntranceId);
            if (entrance == null)
            {
                return false;
            }

            switch (controlDto.Action)
            {
                case BarrierAction.Open:
                    entrance.IsBarrierOpen = true;
                    _barrierStates[controlDto.EntranceId] = true;
                    // 模拟3秒后自动关闭
                    _ = Task.Delay(3000).ContinueWith(_ => {
                        entrance.IsBarrierOpen = false;
                        _barrierStates[controlDto.EntranceId] = false;
                    });
                    break;
                case BarrierAction.Close:
                    entrance.IsBarrierOpen = false;
                    _barrierStates[controlDto.EntranceId] = false;
                    break;
                case BarrierAction.Reset:
                    entrance.IsBarrierOpen = false;
                    _barrierStates[controlDto.EntranceId] = false;
                    break;
                case BarrierAction.Emergency:
                    entrance.IsBarrierOpen = true;
                    _barrierStates[controlDto.EntranceId] = true;
                    break;
            }

            entrance.LastActivity = DateTime.UtcNow;
            return true;
        }

        public async Task<List<EntryNotificationDto>> GetPendingNotificationsAsync(string? entranceId = null)
        {
            var query = _notifications.Where(n => !n.IsHandled);
            
            if (!string.IsNullOrEmpty(entranceId))
            {
                query = query.Where(n => n.VehicleInfo?.EntranceId == entranceId);
            }

            return query.OrderByDescending(n => n.CreatedAt).ToList();
        }

        public async Task<List<EntranceStatusDto>> GetEntranceStatusAsync()
        {
            return _entrances.ToList();
        }

        public async Task<EntryStatisticsDto> GetEntryStatisticsAsync(DateTime date)
        {
            var dayDetections = _detections.Where(d => d.DetectedAt.Date == date.Date).ToList();

            return new EntryStatisticsDto
            {
                Date = date,
                TotalEntries = dayDetections.Count,
                AutomaticEntries = dayDetections.Count(d => d.Status == VehicleEntryStatus.Entered),
                ManualEntries = dayDetections.Count(d => d.Status == VehicleEntryStatus.ManualReview),
                CardEntries = 0, // 需要从其他地方统计
                RejectedEntries = dayDetections.Count(d => d.Status == VehicleEntryStatus.Rejected),
                RecaptureRequests = 0, // 需要从操作记录统计
                AverageProcessingTime = 2.5, // 模拟数据
                TopRejectionReasons = new List<string> { "车位已满", "车牌识别错误", "车辆已在场内" }
            };
        }

        // 私有辅助方法
        private async Task<EntryProcessResultDto> ProcessAutomaticEntryAsync(VehicleDetectionDto detection, EntryDecisionDto decision)
        {
            try
            {
                var entryDto = new CreateVehicleEntryDto
                {
                    LicensePlate = detection.LicensePlate,
                    VehicleType = detection.VehicleType,
                    ImageUrl = detection.ImageUrl,
                    ParkingSpot = await GetAvailableParkingSpotAsync()
                };

                var vehicle = await _parkingService.RecordVehicleEntryAsync(entryDto);
                detection.Status = VehicleEntryStatus.Entered;

                // 自动开闸
                await ControlBarrierAsync(new BarrierControlDto
                {
                    EntranceId = detection.EntranceId,
                    Action = BarrierAction.Open,
                    OperatorId = "SYSTEM",
                    Reason = "自动放行"
                });

                // 创建成功通知（Toast提示）
                await CreateSuccessNotificationAsync(detection, "车辆自动进场成功");

                return new EntryProcessResultDto
                {
                    Success = true,
                    Message = "车辆自动进场成功",
                    Vehicle = vehicle,
                    Decision = decision
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自动进场处理失败: {LicensePlate}", detection.LicensePlate);
                return new EntryProcessResultDto
                {
                    Success = false,
                    Message = $"自动进场失败: {ex.Message}"
                };
            }
        }

        private async Task CreateEntryNotificationAsync(VehicleDetectionDto detection, EntryDecisionDto decision)
        {
            var notification = new EntryNotificationDto
            {
                Type = NotificationType.VehicleBlocked,
                Title = "车辆无法进场",
                Message = $"车牌 {detection.LicensePlate} 无法进场: {decision.Reason}",
                VehicleInfo = detection,
                Decision = decision,
                RequiresAction = true,
                CreatedAt = DateTime.UtcNow
            };

            _notifications.Add(notification);
            detection.Status = VehicleEntryStatus.ManualReview;

            _logger.LogInformation("创建入场通知: {LicensePlate} - {Reason}",
                detection.LicensePlate, decision.Reason);
        }

        private async Task CreateSuccessNotificationAsync(VehicleDetectionDto detection, string message)
        {
            var notification = new EntryNotificationDto
            {
                Type = NotificationType.EntrySuccess,
                Title = "进场成功",
                Message = message,
                VehicleInfo = detection,
                RequiresAction = false,
                CreatedAt = DateTime.UtcNow
            };

            _notifications.Add(notification);
        }

        private async Task<EntryProcessResultDto> ProcessManualAllowAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            try
            {
                var entryDto = new CreateVehicleEntryDto
                {
                    LicensePlate = detection.LicensePlate,
                    VehicleType = detection.VehicleType,
                    ImageUrl = detection.ImageUrl,
                    ParkingSpot = await GetAvailableParkingSpotAsync()
                };

                var vehicle = await _parkingService.RecordVehicleEntryAsync(entryDto);
                detection.Status = VehicleEntryStatus.Entered;

                await ControlBarrierAsync(new BarrierControlDto
                {
                    EntranceId = detection.EntranceId,
                    Action = BarrierAction.Open,
                    OperatorId = actionDto.OperatorId,
                    Reason = "人工放行"
                });

                return new EntryProcessResultDto
                {
                    Success = true,
                    Message = "人工放行成功",
                    Vehicle = vehicle
                };
            }
            catch (Exception ex)
            {
                return new EntryProcessResultDto
                {
                    Success = false,
                    Message = $"人工放行失败: {ex.Message}"
                };
            }
        }

        private async Task<EntryProcessResultDto> ProcessRejectAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            detection.Status = VehicleEntryStatus.Rejected;

            // 确保道闸关闭
            await ControlBarrierAsync(new BarrierControlDto
            {
                EntranceId = detection.EntranceId,
                Action = BarrierAction.Close,
                OperatorId = actionDto.OperatorId,
                Reason = actionDto.Reason
            });

            return new EntryProcessResultDto
            {
                Success = true,
                Message = "车辆进场已拒绝"
            };
        }

        private async Task<EntryProcessResultDto> ProcessManualReleaseAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            try
            {
                var entryDto = new CreateVehicleEntryDto
                {
                    LicensePlate = detection.LicensePlate,
                    VehicleType = detection.VehicleType,
                    ImageUrl = detection.ImageUrl,
                    ParkingSpot = await GetAvailableParkingSpotAsync()
                };

                var vehicle = await _parkingService.RecordVehicleEntryAsync(entryDto);
                detection.Status = VehicleEntryStatus.Entered;

                await ControlBarrierAsync(new BarrierControlDto
                {
                    EntranceId = detection.EntranceId,
                    Action = BarrierAction.Open,
                    OperatorId = actionDto.OperatorId,
                    Reason = "手动放行"
                });

                return new EntryProcessResultDto
                {
                    Success = true,
                    Message = "手动放行成功",
                    Vehicle = vehicle
                };
            }
            catch (Exception ex)
            {
                return new EntryProcessResultDto
                {
                    Success = false,
                    Message = $"手动放行失败: {ex.Message}"
                };
            }
        }

        private async Task<EntryProcessResultDto> ProcessModifyInfoAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            // 修改车牌或车型信息
            if (!string.IsNullOrEmpty(actionDto.ModifiedLicensePlate))
            {
                detection.LicensePlate = actionDto.ModifiedLicensePlate;
            }

            if (actionDto.ModifiedVehicleType.HasValue)
            {
                detection.VehicleType = actionDto.ModifiedVehicleType.Value;
            }

            detection.Status = VehicleEntryStatus.Processing;

            // 重新进行入场决策
            var newDecision = await MakeEntryDecisionAsync(detection);

            if (newDecision.CanEnter && !newDecision.RequiresManualIntervention)
            {
                return await ProcessAutomaticEntryAsync(detection, newDecision);
            }
            else
            {
                await CreateEntryNotificationAsync(detection, newDecision);
                return new EntryProcessResultDto
                {
                    Success = true,
                    Message = "信息已修改，需要重新审核",
                    Decision = newDecision
                };
            }
        }

        private async Task<EntryProcessResultDto> ProcessRecaptureAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            // 模拟重新抓拍
            detection.Confidence = Math.Min(1.0, detection.Confidence + 0.1);
            detection.DetectedAt = DateTime.UtcNow;
            detection.Status = VehicleEntryStatus.Processing;

            // 重新进行入场决策
            var newDecision = await MakeEntryDecisionAsync(detection);

            if (newDecision.CanEnter && !newDecision.RequiresManualIntervention)
            {
                return await ProcessAutomaticEntryAsync(detection, newDecision);
            }
            else
            {
                await CreateEntryNotificationAsync(detection, newDecision);
                return new EntryProcessResultDto
                {
                    Success = true,
                    Message = "重新抓拍完成，需要重新审核",
                    Decision = newDecision
                };
            }
        }

        private async Task<EntryProcessResultDto> ProcessPostponeAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            detection.Status = VehicleEntryStatus.ManualReview;

            return new EntryProcessResultDto
            {
                Success = true,
                Message = "已暂不处理，车辆保持等待状态"
            };
        }

        private async Task<EntryProcessResultDto> ProcessCloseBarrierAsync(VehicleDetectionDto detection, ManualEntryActionDto actionDto)
        {
            await ControlBarrierAsync(new BarrierControlDto
            {
                EntranceId = detection.EntranceId,
                Action = BarrierAction.Close,
                OperatorId = actionDto.OperatorId,
                Reason = "手动关闸"
            });

            return new EntryProcessResultDto
            {
                Success = true,
                Message = "道闸已关闭"
            };
        }

        private async Task<string> GetAvailableParkingSpotAsync()
        {
            // 模拟分配停车位
            var random = new Random();
            var floor = random.Next(1, 4); // 1-3层
            var zone = (char)('A' + random.Next(0, 3)); // A-C区
            var number = random.Next(1, 100); // 1-99号

            return $"{floor}F-{zone}{number:D2}";
        }
    }
}
