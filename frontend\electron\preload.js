const { contextBridge, ipcRenderer } = require('electron')

// 暴露受保护的方法给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 对话框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // 窗口控制
  minimizeWindow: () => ipcRenderer.send('minimize-window'),
  maximizeWindow: () => ipcRenderer.send('maximize-window'),
  closeWindow: () => ipcRenderer.send('close-window'),
  
  // 菜单事件监听
  onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
  onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
  
  // 窗口状态监听
  onWindowMaximized: (callback) => ipcRenderer.on('window-maximized', callback),
  onWindowUnmaximized: (callback) => ipcRenderer.on('window-unmaximized', callback),
  
  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // 系统信息
  platform: process.platform,
  
  // 文件系统操作（安全的）
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
  
  // 打印功能
  print: () => ipcRenderer.invoke('print'),
  printToPDF: (options) => ipcRenderer.invoke('print-to-pdf', options),
  
  // 硬件接口（停车系统专用）
  hardware: {
    // 打印机控制
    printer: {
      print: (data) => ipcRenderer.invoke('hardware-printer-print', data),
      getStatus: () => ipcRenderer.invoke('hardware-printer-status'),
      getPrinters: () => ipcRenderer.invoke('hardware-printer-list')
    },
    
    // 摄像头控制
    camera: {
      capture: () => ipcRenderer.invoke('hardware-camera-capture'),
      getStatus: () => ipcRenderer.invoke('hardware-camera-status'),
      startStream: () => ipcRenderer.invoke('hardware-camera-start'),
      stopStream: () => ipcRenderer.invoke('hardware-camera-stop')
    },
    
    // 读卡器控制
    cardReader: {
      read: () => ipcRenderer.invoke('hardware-card-read'),
      getStatus: () => ipcRenderer.invoke('hardware-card-status')
    },
    
    // 地感传感器
    groundSensor: {
      getStatus: () => ipcRenderer.invoke('hardware-sensor-status'),
      onVehicleDetected: (callback) => ipcRenderer.on('vehicle-detected', callback),
      onVehicleLeft: (callback) => ipcRenderer.on('vehicle-left', callback)
    },
    
    // 道闸控制
    barrier: {
      open: () => ipcRenderer.invoke('hardware-barrier-open'),
      close: () => ipcRenderer.invoke('hardware-barrier-close'),
      getStatus: () => ipcRenderer.invoke('hardware-barrier-status')
    }
  },
  
  // 数据库操作
  database: {
    query: (sql, params) => ipcRenderer.invoke('database-query', sql, params),
    insert: (table, data) => ipcRenderer.invoke('database-insert', table, data),
    update: (table, data, where) => ipcRenderer.invoke('database-update', table, data, where),
    delete: (table, where) => ipcRenderer.invoke('database-delete', table, where)
  },
  
  // 配置管理
  config: {
    get: (key) => ipcRenderer.invoke('config-get', key),
    set: (key, value) => ipcRenderer.invoke('config-set', key, value),
    getAll: () => ipcRenderer.invoke('config-get-all'),
    save: () => ipcRenderer.invoke('config-save')
  },
  
  // 日志记录
  logger: {
    info: (message) => ipcRenderer.send('logger-info', message),
    warn: (message) => ipcRenderer.send('logger-warn', message),
    error: (message) => ipcRenderer.send('logger-error', message),
    debug: (message) => ipcRenderer.send('logger-debug', message)
  },
  
  // 网络请求（代理）
  http: {
    get: (url, options) => ipcRenderer.invoke('http-get', url, options),
    post: (url, data, options) => ipcRenderer.invoke('http-post', url, data, options),
    put: (url, data, options) => ipcRenderer.invoke('http-put', url, data, options),
    delete: (url, options) => ipcRenderer.invoke('http-delete', url, options)
  }
})

// 在窗口加载完成后通知主进程
window.addEventListener('DOMContentLoaded', () => {
  console.log('Electron preload script loaded')
  
  // 可以在这里添加一些初始化逻辑
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector)
    if (element) element.innerText = text
  }

  // 显示版本信息（如果页面有对应元素）
  for (const type of ['chrome', 'node', 'electron']) {
    replaceText(`${type}-version`, process.versions[type])
  }
})

// 错误处理
window.addEventListener('error', (event) => {
  console.error('渲染进程错误:', event.error)
  ipcRenderer.send('renderer-error', {
    message: event.error.message,
    stack: event.error.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
})

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason)
  ipcRenderer.send('renderer-promise-rejection', {
    reason: event.reason,
    promise: event.promise
  })
})
