import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import DashboardView from '../views/DashboardView.vue'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        title: '用户登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      name: 'dashboard',
      component: DashboardView,
      meta: {
        title: '系统首页',
        requiresAuth: true
      }
    },
    {
      path: '/notification-test',
      name: 'notificationTest',
      component: () => import('../views/NotificationTest.vue'),
      meta: {
        title: '通知系统测试',
        requiresAuth: false
      }
    },
    {
      path: '/api-test',
      name: 'apiTest',
      component: () => import('../views/ApiTestView.vue'),
      meta: {
        title: 'API连接测试',
        requiresAuth: false
      }
    },
    {
      path: '/simple-test',
      name: 'simpleTest',
      component: () => import('../views/SimpleApiTest.vue'),
      meta: {
        title: '简单API测试',
        requiresAuth: false
      }
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

// Navigation guard for authentication and page titles
router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore()

  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - HONGRUI停车收费系统`
  } else {
    document.title = 'HONGRUI停车收费系统'
  }

  // Check authentication
  if (to.meta?.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.name === 'login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
