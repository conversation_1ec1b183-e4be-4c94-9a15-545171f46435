export default {
    // 图片链接
    imgUrl: process.env.NODE_ENV !== "development" ?
        window.location.origin + "/img/" : "http://192.168.6.192/img/", //
    // 登陆 + 用户权限
    // login: "/auth/login",
    // logout: "/logout", // 退出登录
    login: "/auth/login", // 登录
    authLogout: "/auth/logout", // 退出登录
    getParkParams: "/parkAuth/getParkParams", //车场授权参数获取 IS_OFFICIAL_EDITION 0:试用版本 1:正式版本
    parkingAuth: "/parkAuth/parkingAuth", //车场授权
    // 公共接口 ： 选项栏列表参数
    getParams: "/public/getParams", // String data
    getDatas: "/public/getDatas", // 获取数据字典中的值  Array dataList
    getCarType: "/public/getCarType", // 获取所有的车类型  Array dataList
    getAllCarType: "/public/getAllCarType", // 获取所有的车类型  Array dataList
    getTempCarType: "/public/getTempCarType", // 获取临时车集  Array dataList
    getMonthCarType: "/public/getMonthCarType", // 获取月租车集  Array dataList
    carTypeAll: "/carType/findAllInfo", // 获取所有的车类型  Array dataList
    getBlendCarType: "/public/getBlendCarType", // 获取月租和储值车类型  Array dataList
    groupFindAll: "/group/findAll", // 获取所有的车位组数据  Array dataList
    // 首页===============================================================
    sumInfoReport: "/home/<USER>/sumInfoReport", // 获取金额统计数据
    statisticsReport: "/home/<USER>/statisticsReport", // 今日收费统计（饼图）
    CarNumReport: "/home/<USER>/CarNumReport", // 车辆进出场数量报表
    getComputerConfigurationusing: "/home/<USER>/getComputerConfigurationusing", // 电脑配置使用情况
    channelStatusReport: "/home/<USER>/channelStatusReport", // 设备在线状态
    couponDetailGetByTransactionId: "/couponDetail/getByTransactionId?transactionId=", // 减免金额详情
    getPictureFind: "/getPicture/find?carNumUid=", // 获取照片
    // 系统管理 ===========================================================
    // 用户管理 ------------------------------------------------------
    findAllUser: "/user/findAllUser", // 获取用户列表
    userInset: "/user/insert", // 新增用户信息
    userUpdate: "/user/update", // 更新用户信息
    userDelete: "/user/delete/", // 删除用户
    resetUserPwd: "/user/resetUserPwd", // 重置密码
    userValiaccount: "/user/valiaccount", // 登录名唯一
    updatePwd: "/user/updatePwd", // 修改密码
    getUserInfo: "/user/findUserByName", // 根据用户登录名，查询当前用户信息
    // 用户组管理 ----------------------------------------------------
    findAllRole: "/role/findAllRole", // 获取所有的用户组信息
    isRepeatRole: "/role/isRepeatRole", // 用户组名称 唯一
    addRole: "/role/addRole", // 保存用户组
    updateRole: "/role/updateRole", // 更新用户组
    deleteRole: "/role/deleteRole", // 删除用户组数据
    updateRoleMenu: "/UserPermissions/updateRoleMenu", // 保存用户组权限
    getRoleMenuView: "/UserPermissions/getRoleMenuView", // 获取用户组权限
    // 软件升级 ------------------------------------------------------
    getVersionData: "/sysVersion/findAll", // 获取升级历史数据
    singleChanelUpgrade: "/sysVersion/singleChanelUpgrade", // 通知书升级
    findLatest: "/sysVersion/findLatest/", // 获取单个软件类型的当前最新版本
    sysVersionInsert: "/sysVersion/insert", // 后台升级  安卓包或语音包升级
    // logo 设置 ----------------------------------------------------
    updateLogo: "/syslogo/updateLogo", // 设置 logo
    // 网络配置 -----------------------------------------------------
    getDualConfig: "/netConfig/getDualConfig/", // 获取网络配置信息
    updateNet: "/netConfig/updateNet", // 保存服务器配置
    netConfigCheckNet: "/netConfig/checkNet", // 测试外网连接状态
    setDate: "/netConfig/setDate", // 同步本机时间
    // 操作日志 -----------------------------------------------------
    findAllLogs: "/syslog/findAllLogs", // 操作日志列表
    // 车场管理 ===========================================================
    // 车场设置 ------------------------------------------------------
    getCarParams: "/paramster/getCarParams", // 获取数据
    batchUpdate: "/paramster/batchUpdate", // 保存设置
    initAuth: "/paramster/initAuth", // 车场授权
    szTraffic: "/szTraffic/find", // 1 深圳数据
    dgTraffic: "/dgTraffic/find", // 2.东莞数据
    ppTraffic: "/ppTraffic/selectAll", // 3.P云数据
    szTrafficSave: `/szTraffic/save`, // 1 深圳数据保存
    dgTrafficSave: "/dgTraffic/save", // 2.东莞数据保存
    ppTrafficSave: "/ppTraffic/save", // 3. P云数据保存

    // 通道设置 ------------------------------------------------------
    channelFind: "/channel/findAllInfo", // 获取通道列表
    channelDelete: "/channel/delete", // 删除单条通道数据
    cleanChannelBing: "/channel/cleanChannelBing", // 解绑单条通道数据
    findByName: "/channel/findByName?name=", // 验证通道名称
    channelSave: "/channel/save", // 保存单条通道信息
    channelGetByCameraIp: "/channel/getByCameraIp", // 校验 IP 是否已被使用
    channelGetByMac: "/channel/getByMac", // 校验 mac 地址是否已存在
    //--------------------高峰模式--------------------------------
    peakFind: "/peak/find/", // 获取单条通道 高峰模式相关信息
    peakInsert: "/peak/insert", // 新增高峰模式
    peakUpdate: "/peak/update", // 更新高峰模式
    //--------------------语音模板--------------------------------
    voiceFind: "/voice/findAllInfo", // 语音模板数据获取
    voiceSet: "/voice/insert", // 设置语音模板
    voiceUpdate: "/voice/update", // 更新语音模板
    //--------------------音量调节--------------------------------
    systemVoiceFind: "/systemVoice/findAll", // 音量调节参数获取
    systemVoiceSet: "/systemVoice/insert", // 音量调节
    systemVoiceUpdate: "/systemVoice/update", // 音量调节
    //--------------------广告用语--------------------------------
    saveMessage: "/paramster/saveMessage", // 设置广告语
    getMessage: "/paramster/getMessage", // 获取广告语
    //--------------------LCD广告图--------------------------------
    findAllImages: "/advertisement/findAll", // 查询全部广告图片
    uploadFile: "/advertisement/uploadFile", // 上传广告图片
    insertImages: "/advertisement/insert", // 保存上传广告图片
    deleteImages: "/advertisement/delete", // 删除广告图片
    // 通道权限 ------------------------------------------------------
    findAllChanPower: "/channel/findAllChanPower", // 通道列表查询
    editChanPower: "/channel/editChanPower", // 权限操作
    // 收费设置 ------------------------------------------------------
    chargeFind: "/charge/findAllInfo", // 查询所有的设置的计费规则信息
    //--------------------跨时段---------------------------------
    chargeGetCharge: "/charge/getCharge", // 获取跨时段，深圳，阶梯收费
    saveCrossPeriodCharge: "/charge/saveCrossPeriodCharge", // 保存
    //--------------------按次收费--------------------------------
    savePreeChage: "/charge/savePreeChage", // 保存
    //--------------------阶梯收费--------------------------------
    saveStepChaarge: "/charge/saveStepChaarge", // 保存
    //--------------------深圳收费--------------------------------
    saveSZChage: "/charge/saveSZChage", // 保存
    initDates: "/noWorkDates/initDates", // 初始化日期
    getAllDates: "/noWorkDates/getAllDates", // 获取日期列表
    bacthUpdate: "/noWorkDates/bacthUpdate", // 保存非工作日日期列表
    //--------------------收费测试------------------------------
    chargeTest: "/charge/chargeTest", //测试应收费用
    // 车类型配置 ----------------------------------------------------
    //carTypeAll
    carTypeUpdate: "/carType/update", // 保存配置
    // 月租费率  -----------------------------------------------------
    monthSetFind: "/monthSet/findAllInfo", //获取月租费率列表数据
    monthSetUpdate: "/monthSet/update", // 保存月租费率
    monthSetInsert: "/monthSet/insert", // 保存月租费率

    // 呼叫中心 ------------------------------------------------------
    findNoBingdingChannel: "/channel/findNoBingdingChannel", // 通道列表
    channelBingding: "/channel/channelBingding", // 绑定通道
    callCenterConfirm: "/callCenter/confirm", // 入场事件 // 出场事件
    callCenterNoCarNoOut: "/callCenter/noCarNoOut", // 出場校正
    findsimilarCar: "/callCenter/findsimilarCar", // // 相似车牌查询
    updateCaNo: "/callCenter/updateCaNo", // 场内校正
    chargeAgain: "/callCenter/chargeAgain", // 选择车牌颜色(车辆类型)后重新计费
    // 监控中心 ------------------------------------------------------
    openOrReshoot: "/callCenter/openOrReshoot?channnelMac=", //	0 开闸  3关闸  1 主相机补拍  offOrOn 打开或关闭 ws
    // 车辆管理============================================================
    // 月租车管理 ----------------------------------------------------
    monthCarFind: "/monthCar/findAllInfo", // 获取月租车数据列表
    downloadAgain: "/monthCar/downloadAgain", // 同步数据到硬件
    monthCarBathDelay: "/monthCar/bathDelay", // 注销选中的月卡
    monthCarFindGroup: "/monthCar/findGroup", // 查看车位租详情
    monthCarSales: "/monthCar/sales/", // 卡销户操作
    // 月租卡续期 --------------------------------------------
    monthCarDelay: "/monthCar/delay", // 延期功能 (按天)
    monthCarBatchDelay: "/monthCar/batchDelay", // 延期功能 （按月）
    monthSetGetUnMon: "/monthSet/getUnMon", // 月租模式变更后，月租费率变更
    // 充值记录 -----------------------------------------------
    monthCarFindRs: "/monthCar/findRs", //缴费历史情况
    // 月租卡发行 ---------------------------------------------
    unquieCarNum: "/monthCar/unquieCarNum", // 车牌唯一性验证
    unquieCarNumUuid: "/monthCar/unquieCarNumUuid", // 车牌唯一性验证
    monthCarSave: "/monthCar/save", // 发行
    // 特殊车管理 ----------------------------------------------------
    specialCarFind: "/specialCar/findAllInfo", // 获取特殊车数据列表
    specialCarDel: "/specialCar/delete/", // 删除特殊车辆
    downLoadFreeCar: "/specialCar/downLoadFreeCar/", // 下载模板


    // 新增编辑 ----------------------------------------------
    specialCarUpdate: "/specialCar/update",
    specialCarInsert: "/specialCar/insert",
    // 临免车管理 ----------------------------------------------------
    freeTypeFind: "/freeType/findAllInfo", // 加载临免车数据
    freeTypeDel: "/freeType/delete/", // 删除选中的特殊车辆
    // 新增编辑 ----------------------------------------------
    freeTypeUpdate: "/freeType/update",
    freeTypeInsert: "/freeType/insert",
    freeTypeFindByName: "/freeType/findByName/", // 名称 唯一
    // 车位租设置 ----------------------------------------------------
    groupFind: "/group/findAllInfo", // 加载车位租数据列表
    groupDel: "/group/delete/", // 删除选中的车位租
    groupDelList: "/group/bathDelete", // 批量删除车位租
    // 新增编辑 ----------------------------------------------
    groupUpdate: "/group/update",
    groupInsert: "/group/insert",
    groupFindByName: "/group/findByName/", // 名称 唯一
    // 车场报表============================================================
    // 出入明细 ------------------------------------------------------
    reportGetCarNumInfo: "/report/getCarNumInfo", // 加载出入明细列表
    getAllCarNumInfo: "/report/getAllCarNumInfo", //导出出入场明细列表
    backspaceCaNo: "/callCenter/backspaceCaNo", //回退场内
    // 场内车辆 ------------------------------------------------------
    getCarInInfo: "/report/getCarInInfo", // 加载场内车辆数据列表
    getAllCarInInfo: "/report/getAllCarInInfo", // 加载场内车辆全部数据列表
    getVehicalDataSummary: "/report/getVehicalDataSummary", // 场内车辆数据汇总
    deleteCarInfo: "/report/deleteCarInfo", // 清理滞留车辆
    downLoadCarIn: "/reportView/downLoadCarIn", //场内车辆Excel模板
    // 收费明细 ------------------------------------------------------
    getChargeInfo: "/report/getChargeInfo", // 加载收费明细列表数据
    getchargeDetail: "/report/getchargeDetail?associateId=", // 查看明细
    getDataSummary: "/report/getDataSummary", // 收费数据汇总
    getAllChargeInfo: "/report/getAllChargeInfo", //导出收费场明细列表
    // 充值明细 ------------------------------------------------------
    //   '/report/getChargeInfo' / 加载收费明细列表数据
    // getDataSummary    // 收费数据汇总
    // 异常开闸记录 --------------------------------------------------
    getIllegalRecord: "/report/getIllegalRecord",
    getIlleVehicalDataSummary: "/report/getIlleVehicalDataSummary", // 异常车辆数据汇总
    // 收费异常记录 ------------------------------------------------------
    getUnusualCarNumInfo: "/report/getUnusualCarNumInfo", // 加载收费异常记录
    getAllUnusualCarNumInfo: "/report/getAllUnusualCarNumInfo", //导出收费异常记录
    // 红包找零记录 --------------------------------------------------
    getRefundRecord: "/report/getRefundRecord", // 加载红包找零数据列表
    getRefoundDataSum: "/report/getRefoundDataSum", // 找零数据汇总
    // 交班记录 ------------------------------------------------------
    getShiftRecord: "/report/getShiftRecord", // 加载交班记录数据列表
    getMonthShiftRecord: "/report/getMonthShiftRecord", // 交班月年报表
    getAllMonthShiftRecord: "/report/getAllMonthShiftRecord", // 交班月/年报表不分页数据
    getAllShiftRecord: "/report/getAllShiftRecord", //  交班报表不分页数据
    setDate: "/systemVoice/setDate", // 同步语音板时间
    // 停车费报表-----------------------------------------------------
    getParkingDayReport: "/report/getParkingDayReport",
    getParkingMonthReport: "/report/getParkingMonthReport",
    getParkingMonthReportAll: "/report/getParkingMonthReportAll",
    // 过车记录-------------------------------------------------------
    getNullah: "/report/getNullah",
    getNullahAll: "/report/getNullahAll",
    // excel 报表下载   ============================================================
    exportMonthChargeRecore: "/reportView/exportMonthChargeRecore", //充值明细导出 excel
    exportRefoundReport: "/reportView/exportRefoundReport", // 红包找零记录
    exportChargeRecore: "/reportView/exportChargeRecore", // 收费明细
    exportCarNum: "/reportView/exportCarNum", // 出入明细
    exportExcel: "/syslog/exportExcel", //操作日志
    exportMonthCarInfo: "/reportView/exportMonthCarInfo", //导出月租卡信息
    downLoadCardTemp: "/monthCar/downLoadCardTemp", //月租卡Excel模板
    downLoadTemp: "/group/downLoadTemp?name=carGroup.xls", // 车位组Excel模板
    // 上传文件 ====================================================================
    // monthCarExcelData: "/monthCar/importExcelData", // 月租车批量导入
    // ============================================================================
};