/**
 * 性能优化工具函数
 */

// 防抖函数 - 优化频繁触发的事件
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// 节流函数 - 限制函数执行频率
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 延迟加载函数
export function lazyLoad<T>(
  loader: () => Promise<T>,
  delay = 0
): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(loader())
    }, delay)
  })
}

// 内存优化 - 清理大对象
export function clearLargeObject(obj: any): void {
  if (obj && typeof obj === 'object') {
    Object.keys(obj).forEach(key => {
      delete obj[key]
    })
  }
}

// 批量更新DOM - 减少重绘
export function batchUpdate(updates: (() => void)[]): void {
  requestAnimationFrame(() => {
    updates.forEach(update => update())
  })
}

// 虚拟滚动辅助函数
export function calculateVisibleItems(
  containerHeight: number,
  itemHeight: number,
  scrollTop: number,
  totalItems: number,
  buffer = 5
) {
  const visibleCount = Math.ceil(containerHeight / itemHeight)
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer)
  const endIndex = Math.min(totalItems - 1, startIndex + visibleCount + buffer * 2)
  
  return {
    startIndex,
    endIndex,
    visibleCount,
    offsetY: startIndex * itemHeight
  }
}

// 图片懒加载
export function createImageLazyLoader() {
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement
        const src = img.dataset.src
        if (src) {
          img.src = src
          img.removeAttribute('data-src')
          imageObserver.unobserve(img)
        }
      }
    })
  })
  
  return {
    observe: (img: HTMLImageElement) => imageObserver.observe(img),
    disconnect: () => imageObserver.disconnect()
  }
}

// 性能监控
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number> = new Map()
  
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }
  
  // 开始计时
  startTiming(label: string): void {
    this.metrics.set(label, performance.now())
  }
  
  // 结束计时并返回耗时
  endTiming(label: string): number {
    const startTime = this.metrics.get(label)
    if (startTime) {
      const duration = performance.now() - startTime
      this.metrics.delete(label)
      console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`)
      return duration
    }
    return 0
  }
  
  // 测量函数执行时间
  measureFunction<T extends (...args: any[]) => any>(
    func: T,
    label?: string
  ): (...args: Parameters<T>) => ReturnType<T> {
    return (...args: Parameters<T>): ReturnType<T> => {
      const functionLabel = label || func.name || 'anonymous'
      this.startTiming(functionLabel)
      const result = func(...args)
      this.endTiming(functionLabel)
      return result
    }
  }
  
  // 获取内存使用情况
  getMemoryUsage(): any {
    if ('memory' in performance) {
      return {
        used: Math.round((performance as any).memory.usedJSHeapSize / 1048576),
        total: Math.round((performance as any).memory.totalJSHeapSize / 1048576),
        limit: Math.round((performance as any).memory.jsHeapSizeLimit / 1048576)
      }
    }
    return null
  }
  
  // 监控FPS
  monitorFPS(callback: (fps: number) => void): () => void {
    let frames = 0
    let lastTime = performance.now()
    let animationId: number
    
    function countFrames() {
      frames++
      const currentTime = performance.now()
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frames * 1000) / (currentTime - lastTime))
        callback(fps)
        frames = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(countFrames)
    }
    
    animationId = requestAnimationFrame(countFrames)
    
    return () => cancelAnimationFrame(animationId)
  }
}

// 缓存管理
export class CacheManager {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map()
  
  // 设置缓存
  set(key: string, data: any, ttl = 300000): void { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  // 获取缓存
  get(key: string): any {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  // 清理过期缓存
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
  
  // 清空所有缓存
  clear(): void {
    this.cache.clear()
  }
  
  // 获取缓存大小
  size(): number {
    return this.cache.size
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()
export const cacheManager = new CacheManager()

// 自动清理缓存
setInterval(() => {
  cacheManager.cleanup()
}, 60000) // 每分钟清理一次过期缓存
