using Microsoft.AspNetCore.Mvc;
using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;

namespace ParkingBoothApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HardwareController : ControllerBase
    {
        private readonly IHardwareService _hardwareService;
        private readonly ILogger<HardwareController> _logger;

        public HardwareController(IHardwareService hardwareService, ILogger<HardwareController> logger)
        {
            _hardwareService = hardwareService;
            _logger = logger;
        }

        /// <summary>
        /// Get status of all hardware devices
        /// </summary>
        [HttpGet("status")]
        public async Task<ActionResult<HardwareStatusDto>> GetHardwareStatus()
        {
            try
            {
                var status = await _hardwareService.GetAllHardwareStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving hardware status");
                return StatusCode(500, "An error occurred while retrieving hardware status");
            }
        }

        /// <summary>
        /// Refresh hardware status
        /// </summary>
        [HttpPost("status/refresh")]
        public async Task<ActionResult> RefreshHardwareStatus()
        {
            try
            {
                await _hardwareService.RefreshHardwareStatusAsync();
                return Ok(new { message = "Hardware status refreshed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing hardware status");
                return StatusCode(500, "An error occurred while refreshing hardware status");
            }
        }

        #region Printer Operations

        /// <summary>
        /// Get printer status
        /// </summary>
        [HttpGet("printer/status")]
        public async Task<ActionResult<PrinterStatusDto>> GetPrinterStatus()
        {
            try
            {
                var status = await _hardwareService.GetPrinterStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving printer status");
                return StatusCode(500, "An error occurred while retrieving printer status");
            }
        }

        /// <summary>
        /// Print a ticket
        /// </summary>
        [HttpPost("printer/print")]
        public async Task<ActionResult<HardwareResponseDto>> PrintTicket([FromBody] PrintTicketDto printDto)
        {
            try
            {
                _logger.LogInformation("Printing ticket of type: {Type}", printDto.Type);
                var result = await _hardwareService.PrintTicketAsync(printDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error printing ticket");
                return StatusCode(500, "An error occurred while printing ticket");
            }
        }

        /// <summary>
        /// Test printer functionality
        /// </summary>
        [HttpPost("printer/test")]
        public async Task<ActionResult<HardwareResponseDto>> TestPrinter()
        {
            try
            {
                var result = await _hardwareService.TestPrinterAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing printer");
                return StatusCode(500, "An error occurred while testing printer");
            }
        }

        /// <summary>
        /// Reset printer
        /// </summary>
        [HttpPost("printer/reset")]
        public async Task<ActionResult<HardwareResponseDto>> ResetPrinter()
        {
            try
            {
                var result = await _hardwareService.ResetPrinterAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting printer");
                return StatusCode(500, "An error occurred while resetting printer");
            }
        }

        #endregion

        #region Camera Operations

        /// <summary>
        /// Get camera status
        /// </summary>
        [HttpGet("camera/status")]
        public async Task<ActionResult<CameraStatusDto>> GetCameraStatus()
        {
            try
            {
                var status = await _hardwareService.GetCameraStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving camera status");
                return StatusCode(500, "An error occurred while retrieving camera status");
            }
        }

        /// <summary>
        /// Capture an image
        /// </summary>
        [HttpPost("camera/capture")]
        public async Task<ActionResult<ImageCaptureResponseDto>> CaptureImage([FromBody] CaptureImageDto? captureDto = null)
        {
            try
            {
                captureDto ??= new CaptureImageDto();
                _logger.LogInformation("Capturing image for purpose: {Purpose}", captureDto.Purpose);
                var result = await _hardwareService.CaptureImageAsync(captureDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error capturing image");
                return StatusCode(500, "An error occurred while capturing image");
            }
        }

        /// <summary>
        /// Test camera functionality
        /// </summary>
        [HttpPost("camera/test")]
        public async Task<ActionResult<HardwareResponseDto>> TestCamera()
        {
            try
            {
                var result = await _hardwareService.TestCameraAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing camera");
                return StatusCode(500, "An error occurred while testing camera");
            }
        }

        /// <summary>
        /// Reset camera
        /// </summary>
        [HttpPost("camera/reset")]
        public async Task<ActionResult<HardwareResponseDto>> ResetCamera()
        {
            try
            {
                var result = await _hardwareService.ResetCameraAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting camera");
                return StatusCode(500, "An error occurred while resetting camera");
            }
        }

        #endregion

        #region Card Reader Operations

        /// <summary>
        /// Get card reader status
        /// </summary>
        [HttpGet("cardreader/status")]
        public async Task<ActionResult<CardReaderStatusDto>> GetCardReaderStatus()
        {
            try
            {
                var status = await _hardwareService.GetCardReaderStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving card reader status");
                return StatusCode(500, "An error occurred while retrieving card reader status");
            }
        }

        /// <summary>
        /// Read a card
        /// </summary>
        [HttpPost("cardreader/read")]
        public async Task<ActionResult<CardReadResponseDto>> ReadCard()
        {
            try
            {
                _logger.LogInformation("Reading card");
                var result = await _hardwareService.ReadCardAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading card");
                return StatusCode(500, "An error occurred while reading card");
            }
        }

        /// <summary>
        /// Process card payment
        /// </summary>
        [HttpPost("cardreader/payment")]
        public async Task<ActionResult<CardPaymentResponseDto>> ProcessCardPayment([FromBody] CardPaymentDto paymentDto)
        {
            try
            {
                _logger.LogInformation("Processing card payment for amount: {Amount}", paymentDto.Amount);
                var result = await _hardwareService.ProcessCardPaymentAsync(paymentDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing card payment");
                return StatusCode(500, "An error occurred while processing card payment");
            }
        }

        /// <summary>
        /// Test card reader functionality
        /// </summary>
        [HttpPost("cardreader/test")]
        public async Task<ActionResult<HardwareResponseDto>> TestCardReader()
        {
            try
            {
                var result = await _hardwareService.TestCardReaderAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing card reader");
                return StatusCode(500, "An error occurred while testing card reader");
            }
        }

        /// <summary>
        /// Reset card reader
        /// </summary>
        [HttpPost("cardreader/reset")]
        public async Task<ActionResult<HardwareResponseDto>> ResetCardReader()
        {
            try
            {
                var result = await _hardwareService.ResetCardReaderAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting card reader");
                return StatusCode(500, "An error occurred while resetting card reader");
            }
        }

        #endregion

        #region Ground Sensor Operations

        /// <summary>
        /// Get ground sensor status
        /// </summary>
        [HttpGet("groundsensor/status")]
        public async Task<ActionResult<GroundSensorStatusDto>> GetGroundSensorStatus()
        {
            try
            {
                var status = await _hardwareService.GetGroundSensorStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving ground sensor status");
                return StatusCode(500, "An error occurred while retrieving ground sensor status");
            }
        }

        /// <summary>
        /// Check for vehicle presence
        /// </summary>
        [HttpGet("groundsensor/detect")]
        public async Task<ActionResult<VehicleDetectionResponseDto>> CheckVehiclePresence()
        {
            try
            {
                var result = await _hardwareService.CheckVehiclePresenceAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking vehicle presence");
                return StatusCode(500, "An error occurred while checking vehicle presence");
            }
        }

        /// <summary>
        /// Test ground sensor functionality
        /// </summary>
        [HttpPost("groundsensor/test")]
        public async Task<ActionResult<HardwareResponseDto>> TestGroundSensor()
        {
            try
            {
                var result = await _hardwareService.TestGroundSensorAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing ground sensor");
                return StatusCode(500, "An error occurred while testing ground sensor");
            }
        }

        /// <summary>
        /// Reset ground sensor
        /// </summary>
        [HttpPost("groundsensor/reset")]
        public async Task<ActionResult<HardwareResponseDto>> ResetGroundSensor()
        {
            try
            {
                var result = await _hardwareService.ResetGroundSensorAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting ground sensor");
                return StatusCode(500, "An error occurred while resetting ground sensor");
            }
        }

        #endregion

        #region Diagnostics and Maintenance

        /// <summary>
        /// Run full hardware diagnostics
        /// </summary>
        [HttpPost("diagnostics")]
        public async Task<ActionResult<HardwareDiagnosticsDto>> RunDiagnostics()
        {
            try
            {
                _logger.LogInformation("Running full hardware diagnostics");
                var result = await _hardwareService.RunFullDiagnosticsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running hardware diagnostics");
                return StatusCode(500, "An error occurred while running diagnostics");
            }
        }

        /// <summary>
        /// Execute custom hardware command
        /// </summary>
        [HttpPost("command")]
        public async Task<ActionResult<HardwareResponseDto>> ExecuteCommand([FromBody] HardwareCommandDto commandDto)
        {
            try
            {
                _logger.LogInformation("Executing hardware command: {Command} on {DeviceType}", 
                    commandDto.Command, commandDto.DeviceType);
                var result = await _hardwareService.ExecuteHardwareCommandAsync(commandDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing hardware command");
                return StatusCode(500, "An error occurred while executing hardware command");
            }
        }

        #endregion
    }
}
