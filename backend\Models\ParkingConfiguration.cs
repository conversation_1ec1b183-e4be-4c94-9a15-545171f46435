using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ParkingBoothApi.Models
{
    public class ParkingConfiguration
    {
        [Key]
        public string Id { get; set; } = "default";
        
        [Required]
        [Range(1, 10000)]
        public int MaxCapacity { get; set; } = 100;
        
        [Required]
        public ParkingRates Rates { get; set; } = new();
        
        [Required]
        public ParkingRules Rules { get; set; } = new();
        
        public HardwareConfiguration Hardware { get; set; } = new();
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
    
    public class ParkingRates
    {
        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Range(0, 1000)]
        public decimal CarHourlyRate { get; set; } = 2.0m;
        
        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Range(0, 1000)]
        public decimal MotorcycleHourlyRate { get; set; } = 1.0m;
        
        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Range(0, 1000)]
        public decimal TruckHourlyRate { get; set; } = 3.0m;
        
        // Weekend/Holiday multipliers
        [Column(TypeName = "decimal(3,2)")]
        [Range(0.5, 5.0)]
        public decimal WeekendMultiplier { get; set; } = 1.0m;
        
        [Column(TypeName = "decimal(3,2)")]
        [Range(0.5, 5.0)]
        public decimal HolidayMultiplier { get; set; } = 1.0m;
    }
    
    public class ParkingRules
    {
        [Required]
        [Column(TypeName = "decimal(10,2)")]
        [Range(0, 100)]
        public decimal MinimumFee { get; set; } = 1.0m;
        
        [Required]
        [Range(0, 120)]
        public int GracePeriodMinutes { get; set; } = 15;
        
        [Required]
        [Range(1, 24)]
        public int MaxParkingHours { get; set; } = 24;
        
        [Column(TypeName = "decimal(10,2)")]
        [Range(0, 1000)]
        public decimal OvertimePenalty { get; set; } = 10.0m;
        
        public bool AllowOvernightParking { get; set; } = true;
        
        public TimeSpan? NightRateStartTime { get; set; } = new TimeSpan(18, 0, 0); // 6 PM
        
        public TimeSpan? NightRateEndTime { get; set; } = new TimeSpan(8, 0, 0); // 8 AM
        
        [Column(TypeName = "decimal(3,2)")]
        [Range(0.1, 2.0)]
        public decimal NightRateMultiplier { get; set; } = 0.5m;
    }
    
    public class HardwareConfiguration
    {
        public bool AutoCaptureOnEntry { get; set; } = true;
        
        public bool AutoPrintReceipts { get; set; } = true;
        
        public string CameraResolution { get; set; } = "1920x1080";
        
        [Range(1, 10)]
        public int GroundSensorSensitivity { get; set; } = 5;
        
        [Range(1, 60)]
        public int StatusCheckIntervalSeconds { get; set; } = 30;
        
        [Range(1, 10)]
        public int MaxRetryAttempts { get; set; } = 3;
        
        [Range(1000, 30000)]
        public int CommandTimeoutMs { get; set; } = 5000;
        
        public Dictionary<string, string> DeviceSettings { get; set; } = new();
    }
    
    public class SystemStatistics
    {
        public DateTime Date { get; set; }
        
        public int TotalVehicles { get; set; }
        
        public int CurrentOccupancy { get; set; }
        
        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalRevenue { get; set; }
        
        public int AverageStayTimeMinutes { get; set; }
        
        public int PeakOccupancy { get; set; }
        
        public TimeSpan? PeakTime { get; set; }
        
        public Dictionary<VehicleType, int> VehicleTypeBreakdown { get; set; } = new();
        
        public Dictionary<PaymentMethod, decimal> PaymentMethodBreakdown { get; set; } = new();
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
