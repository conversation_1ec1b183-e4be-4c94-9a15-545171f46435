/**
 * 全局通知系统
 * 用于替换原生的alert、confirm等弹窗
 */

interface NotificationOptions {
  title?: string
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

interface ConfirmOptions {
  title?: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'warning' | 'info' | 'error'
}

class NotificationService {
  private notificationComponent: any = null

  // 设置通知组件实例
  setNotificationComponent(component: any) {
    this.notificationComponent = component
  }

  // 显示成功通知
  success(message: string, title?: string, duration?: number) {
    if (this.notificationComponent) {
      return this.notificationComponent.addNotification({
        type: 'success',
        message,
        title,
        duration
      })
    }
    console.log('✅', title || '成功', message)
  }

  // 显示错误通知
  error(message: string, title?: string, duration?: number) {
    if (this.notificationComponent) {
      return this.notificationComponent.addNotification({
        type: 'error',
        message,
        title,
        duration
      })
    }
    console.error('❌', title || '错误', message)
  }

  // 显示警告通知
  warning(message: string, title?: string, duration?: number) {
    if (this.notificationComponent) {
      return this.notificationComponent.addNotification({
        type: 'warning',
        message,
        title,
        duration
      })
    }
    console.warn('⚠️', title || '警告', message)
  }

  // 显示信息通知
  info(message: string, title?: string, duration?: number) {
    if (this.notificationComponent) {
      return this.notificationComponent.addNotification({
        type: 'info',
        message,
        title,
        duration
      })
    }
    console.info('ℹ️', title || '信息', message)
  }

  // 通用通知方法
  notify(options: NotificationOptions) {
    const { type = 'info', message, title, duration } = options
    
    switch (type) {
      case 'success':
        return this.success(message, title, duration)
      case 'error':
        return this.error(message, title, duration)
      case 'warning':
        return this.warning(message, title, duration)
      case 'info':
      default:
        return this.info(message, title, duration)
    }
  }

  // 美化的确认对话框
  confirm(options: ConfirmOptions): Promise<boolean> {
    return new Promise((resolve) => {
      // 创建确认对话框元素
      const overlay = document.createElement('div')
      overlay.className = 'confirm-overlay'
      
      const modal = document.createElement('div')
      modal.className = 'confirm-modal'
      
      const { title, message, confirmText = '确认', cancelText = '取消', type = 'warning' } = options
      
      // 图标映射
      const iconMap = {
        warning: '⚠️',
        info: 'ℹ️',
        error: '❌'
      }
      
      // 颜色映射
      const colorMap = {
        warning: '#ffc107',
        info: '#17a2b8',
        error: '#dc3545'
      }
      
      modal.innerHTML = `
        <div class="confirm-header">
          <div class="confirm-icon" style="color: ${colorMap[type]}">
            ${iconMap[type]}
          </div>
          ${title ? `<h3 class="confirm-title">${title}</h3>` : ''}
        </div>
        <div class="confirm-body">
          <p class="confirm-message">${message}</p>
        </div>
        <div class="confirm-footer">
          <button class="confirm-btn cancel">${cancelText}</button>
          <button class="confirm-btn confirm" style="background: ${colorMap[type]}">${confirmText}</button>
        </div>
      `
      
      // 添加停车系统专业样式
      const style = document.createElement('style')
      style.textContent = `
        .confirm-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(44, 62, 80, 0.75);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 400000;
          animation: fadeIn 0.25s ease-out;
          font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .confirm-modal {
          background: #ffffff;
          border-radius: 8px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
          min-width: 420px;
          max-width: 500px;
          overflow: hidden;
          animation: slideUp 0.25s ease-out;
          border: 1px solid #e1e5e9;
        }

        .confirm-header {
          padding: 24px 24px 20px;
          text-align: center;
          background: #f8f9fa;
          border-bottom: 1px solid #e1e5e9;
        }

        .confirm-icon {
          font-size: 40px;
          margin-bottom: 12px;
          opacity: 0.9;
        }

        .confirm-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
        }

        .confirm-body {
          padding: 24px;
          text-align: center;
        }

        .confirm-message {
          margin: 0;
          font-size: 14px;
          color: #495057;
          line-height: 1.6;
        }

        .confirm-footer {
          padding: 16px 24px 24px;
          display: flex;
          gap: 12px;
          justify-content: center;
          background: #f8f9fa;
          border-top: 1px solid #e1e5e9;
        }

        .confirm-btn {
          padding: 10px 20px;
          border: 1px solid;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          min-width: 90px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
        }

        .confirm-btn.cancel {
          background: #ffffff;
          border-color: #6c757d;
          color: #6c757d;
        }

        .confirm-btn.cancel:hover {
          background: #6c757d;
          color: #ffffff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        .confirm-btn.confirm {
          background: #ffffff;
          border-color: currentColor;
          color: inherit;
        }

        .confirm-btn.confirm:hover {
          background: currentColor;
          color: #ffffff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.98);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @media (max-width: 768px) {
          .confirm-modal {
            min-width: 320px;
            margin: 0 20px;
          }

          .confirm-header {
            padding: 20px 20px 16px;
          }

          .confirm-body {
            padding: 20px;
          }

          .confirm-footer {
            padding: 16px 20px 20px;
            flex-direction: column;
          }

          .confirm-btn {
            width: 100%;
          }
        }
      `
      
      document.head.appendChild(style)
      overlay.appendChild(modal)
      document.body.appendChild(overlay)
      
      // 事件处理
      const cleanup = () => {
        document.body.removeChild(overlay)
        document.head.removeChild(style)
      }
      
      const cancelBtn = modal.querySelector('.cancel')
      const confirmBtn = modal.querySelector('.confirm')
      
      cancelBtn?.addEventListener('click', () => {
        cleanup()
        resolve(false)
      })
      
      confirmBtn?.addEventListener('click', () => {
        cleanup()
        resolve(true)
      })
      
      // 点击外部关闭
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          cleanup()
          resolve(false)
        }
      })
      
      // ESC键关闭
      const handleKeydown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          cleanup()
          document.removeEventListener('keydown', handleKeydown)
          resolve(false)
        }
      }
      document.addEventListener('keydown', handleKeydown)
    })
  }

  // 清除所有通知
  clearAll() {
    if (this.notificationComponent) {
      this.notificationComponent.clearAll()
    }
  }
}

// 创建全局实例
export const notification = new NotificationService()

// 便捷方法
export const showSuccess = (message: string, title?: string) => notification.success(message, title)
export const showError = (message: string, title?: string) => notification.error(message, title)
export const showWarning = (message: string, title?: string) => notification.warning(message, title)
export const showInfo = (message: string, title?: string) => notification.info(message, title)
export const showConfirm = (message: string, title?: string) => notification.confirm({ message, title })

export default notification
