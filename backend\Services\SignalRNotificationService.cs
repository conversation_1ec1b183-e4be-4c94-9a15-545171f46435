using Microsoft.AspNetCore.SignalR;
using ParkingBoothApi.Hubs;
using ParkingBoothApi.Interfaces;

namespace ParkingBoothApi.Services
{
    public interface ISignalRNotificationService
    {
        Task SendHardwareStatusUpdateAsync(string boothId, string deviceType, string status, bool connected, Dictionary<string, object>? data = null);
        Task SendVehicleDetectionAsync(string boothId, bool vehicleDetected, int sensorReading, string? message = null);
        Task SendPaymentCompletedAsync(string boothId, string transactionId, string licensePlate, decimal amount, string paymentMethod, bool success = true);
        Task SendSystemNotificationAsync(string boothId, string type, string title, string message, bool autoClose = true);
        Task SendToAllBoothsAsync(string method, object data);
        Task SendToBoothAsync(string boothId, string method, object data);
    }

    public class SignalRNotificationService : ISignalRNotificationService
    {
        private readonly IHubContext<ParkingHub> _hubContext;
        private readonly ILogger<SignalRNotificationService> _logger;

        public SignalRNotificationService(IHubContext<ParkingHub> hubContext, ILogger<SignalRNotificationService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task SendHardwareStatusUpdateAsync(string boothId, string deviceType, string status, bool connected, Dictionary<string, object>? data = null)
        {
            var update = new HardwareStatusUpdateDto
            {
                DeviceType = deviceType,
                Status = status,
                Connected = connected,
                Data = data,
                Timestamp = DateTime.UtcNow
            };

            await _hubContext.Clients.Group($"Booth_{boothId}").SendAsync("HardwareStatusUpdate", update);
            _logger.LogInformation("Hardware status update sent for {DeviceType} in booth {BoothId}: {Status}", deviceType, boothId, status);
        }

        public async Task SendVehicleDetectionAsync(string boothId, bool vehicleDetected, int sensorReading, string? message = null)
        {
            var detection = new VehicleDetectionEventDto
            {
                VehicleDetected = vehicleDetected,
                SensorReading = sensorReading,
                Message = message,
                Timestamp = DateTime.UtcNow
            };

            await _hubContext.Clients.Group($"Booth_{boothId}").SendAsync("VehicleDetection", detection);
            _logger.LogInformation("Vehicle detection event sent for booth {BoothId}: {VehicleDetected}", boothId, vehicleDetected);
        }

        public async Task SendPaymentCompletedAsync(string boothId, string transactionId, string licensePlate, decimal amount, string paymentMethod, bool success = true)
        {
            var payment = new PaymentCompletedEventDto
            {
                TransactionId = transactionId,
                LicensePlate = licensePlate,
                Amount = amount,
                PaymentMethod = paymentMethod,
                Success = success,
                Timestamp = DateTime.UtcNow
            };

            await _hubContext.Clients.Group($"Booth_{boothId}").SendAsync("PaymentCompleted", payment);
            _logger.LogInformation("Payment completed event sent for booth {BoothId}: {TransactionId} - {Amount}", boothId, transactionId, amount);
        }

        public async Task SendSystemNotificationAsync(string boothId, string type, string title, string message, bool autoClose = true)
        {
            var notification = new SystemNotificationDto
            {
                Type = type,
                Title = title,
                Message = message,
                AutoClose = autoClose,
                Timestamp = DateTime.UtcNow
            };

            await _hubContext.Clients.Group($"Booth_{boothId}").SendAsync("SystemNotification", notification);
            _logger.LogInformation("System notification sent to booth {BoothId}: {Type} - {Title}", boothId, type, title);
        }

        public async Task SendToAllBoothsAsync(string method, object data)
        {
            await _hubContext.Clients.All.SendAsync(method, data);
            _logger.LogInformation("Message sent to all booths: {Method}", method);
        }

        public async Task SendToBoothAsync(string boothId, string method, object data)
        {
            await _hubContext.Clients.Group($"Booth_{boothId}").SendAsync(method, data);
            _logger.LogInformation("Message sent to booth {BoothId}: {Method}", boothId, method);
        }
    }
}
