using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;
using ParkingBoothApi.Models;

namespace ParkingBoothApi.Services
{
    public class HardwareService : IHardwareService
    {
        private readonly ILogger<HardwareService> _logger;
        private readonly ISignalRNotificationService _signalRService;
        private static HardwareStatus _hardwareStatus = new();
        private static HardwareConfiguration _hardwareConfig = new();
        private const string BOOTH_ID = "001"; // This could be configurable

        public HardwareService(ILogger<HardwareService> logger, ISignalRNotificationService signalRService)
        {
            _logger = logger;
            _signalRService = signalRService;
            InitializeHardwareStatus();
        }

        private void InitializeHardwareStatus()
        {
            // Simulate hardware initialization
            _hardwareStatus = new HardwareStatus
            {
                Printer = new PrinterStatus
                {
                    Connected = true,
                    Status = DeviceStatus.Ready,
                    PaperLevel = 85
                },
                Camera = new CameraStatus
                {
                    Connected = true,
                    Status = DeviceStatus.Ready,
                    Resolution = "1920x1080"
                },
                CardReader = new CardReaderStatus
                {
                    Connected = true,
                    Status = DeviceStatus.Ready
                },
                GroundSensor = new GroundSensorStatus
                {
                    Connected = false, // Simulate one device offline
                    Status = DeviceStatus.Offline,
                    VehicleDetected = false
                },
                LastUpdated = DateTime.UtcNow
            };
        }

        public async Task<HardwareStatusDto> GetAllHardwareStatusAsync()
        {
            await RefreshHardwareStatusAsync();
            
            return new HardwareStatusDto
            {
                Printer = new PrinterStatusDto
                {
                    Connected = _hardwareStatus.Printer.Connected,
                    Status = _hardwareStatus.Printer.Status.ToString().ToLower(),
                    PaperLevel = _hardwareStatus.Printer.PaperLevel,
                    LastError = _hardwareStatus.Printer.LastError,
                    LastPrintTime = _hardwareStatus.Printer.LastPrintTime
                },
                Camera = new CameraStatusDto
                {
                    Connected = _hardwareStatus.Camera.Connected,
                    Status = _hardwareStatus.Camera.Status.ToString().ToLower(),
                    Resolution = _hardwareStatus.Camera.Resolution,
                    LastCapture = _hardwareStatus.Camera.LastCapture
                },
                CardReader = new CardReaderStatusDto
                {
                    Connected = _hardwareStatus.CardReader.Connected,
                    Status = _hardwareStatus.CardReader.Status.ToString().ToLower(),
                    LastTransaction = _hardwareStatus.CardReader.LastTransaction,
                    Balance = _hardwareStatus.CardReader.LastCardBalance
                },
                GroundSensor = new GroundSensorStatusDto
                {
                    Connected = _hardwareStatus.GroundSensor.Connected,
                    Status = _hardwareStatus.GroundSensor.Status.ToString().ToLower(),
                    VehicleDetected = _hardwareStatus.GroundSensor.VehicleDetected,
                    LastDetection = _hardwareStatus.GroundSensor.LastDetection
                },
                LastUpdated = _hardwareStatus.LastUpdated
            };
        }

        public async Task<PrinterStatusDto> GetPrinterStatusAsync()
        {
            return await Task.FromResult(new PrinterStatusDto
            {
                Connected = _hardwareStatus.Printer.Connected,
                Status = _hardwareStatus.Printer.Status.ToString().ToLower(),
                PaperLevel = _hardwareStatus.Printer.PaperLevel,
                LastError = _hardwareStatus.Printer.LastError,
                LastPrintTime = _hardwareStatus.Printer.LastPrintTime
            });
        }

        public async Task<CameraStatusDto> GetCameraStatusAsync()
        {
            return await Task.FromResult(new CameraStatusDto
            {
                Connected = _hardwareStatus.Camera.Connected,
                Status = _hardwareStatus.Camera.Status.ToString().ToLower(),
                Resolution = _hardwareStatus.Camera.Resolution,
                LastCapture = _hardwareStatus.Camera.LastCapture
            });
        }

        public async Task<CardReaderStatusDto> GetCardReaderStatusAsync()
        {
            return await Task.FromResult(new CardReaderStatusDto
            {
                Connected = _hardwareStatus.CardReader.Connected,
                Status = _hardwareStatus.CardReader.Status.ToString().ToLower(),
                LastTransaction = _hardwareStatus.CardReader.LastTransaction,
                Balance = _hardwareStatus.CardReader.LastCardBalance
            });
        }

        public async Task<GroundSensorStatusDto> GetGroundSensorStatusAsync()
        {
            return await Task.FromResult(new GroundSensorStatusDto
            {
                Connected = _hardwareStatus.GroundSensor.Connected,
                Status = _hardwareStatus.GroundSensor.Status.ToString().ToLower(),
                VehicleDetected = _hardwareStatus.GroundSensor.VehicleDetected,
                LastDetection = _hardwareStatus.GroundSensor.LastDetection
            });
        }

        public async Task RefreshHardwareStatusAsync()
        {
            _logger.LogInformation("Refreshing hardware status");

            // Simulate hardware status check
            await Task.Delay(100);

            // Randomly simulate some status changes for demo
            var random = new Random();
            var oldPaperLevel = _hardwareStatus.Printer.PaperLevel;
            var oldVehicleDetected = _hardwareStatus.GroundSensor.VehicleDetected;

            _hardwareStatus.Printer.PaperLevel = Math.Max(0, _hardwareStatus.Printer.PaperLevel - random.Next(0, 5));
            _hardwareStatus.GroundSensor.VehicleDetected = random.Next(0, 10) < 3; // 30% chance

            if (_hardwareStatus.GroundSensor.VehicleDetected)
            {
                _hardwareStatus.GroundSensor.LastDetection = DateTime.UtcNow;
            }

            _hardwareStatus.LastUpdated = DateTime.UtcNow;

            // Send SignalR notifications for significant changes
            if (oldPaperLevel != _hardwareStatus.Printer.PaperLevel)
            {
                await _signalRService.SendHardwareStatusUpdateAsync(BOOTH_ID, "printer", "ready", true,
                    new Dictionary<string, object> { ["paperLevel"] = _hardwareStatus.Printer.PaperLevel });
            }

            if (oldVehicleDetected != _hardwareStatus.GroundSensor.VehicleDetected)
            {
                await _signalRService.SendVehicleDetectionAsync(BOOTH_ID, _hardwareStatus.GroundSensor.VehicleDetected,
                    random.Next(0, 100), _hardwareStatus.GroundSensor.VehicleDetected ? "Vehicle detected" : "No vehicle detected");
            }
        }

        public async Task<HardwareResponseDto> PrintTicketAsync(PrintTicketDto printDto)
        {
            _logger.LogInformation("Printing ticket of type: {Type}", printDto.Type);
            
            if (!_hardwareStatus.Printer.Connected)
            {
                return new HardwareResponseDto
                {
                    Success = false,
                    Message = "Printer is not connected"
                };
            }

            // Simulate printing
            _hardwareStatus.Printer.Status = DeviceStatus.Busy;
            await Task.Delay(2000); // Simulate print time
            
            _hardwareStatus.Printer.Status = DeviceStatus.Ready;
            _hardwareStatus.Printer.LastPrintTime = DateTime.UtcNow;
            _hardwareStatus.Printer.PaperLevel = Math.Max(0, _hardwareStatus.Printer.PaperLevel - 1);

            return new HardwareResponseDto
            {
                Success = true,
                Message = "Ticket printed successfully",
                Data = new Dictionary<string, object>
                {
                    ["printTime"] = DateTime.UtcNow,
                    ["paperLevel"] = _hardwareStatus.Printer.PaperLevel
                }
            };
        }

        public async Task<HardwareResponseDto> TestPrinterAsync()
        {
            _logger.LogInformation("Testing printer");
            
            var testPrint = new PrintTicketDto
            {
                Type = "test",
                Timestamp = DateTime.UtcNow
            };
            
            return await PrintTicketAsync(testPrint);
        }

        public async Task<HardwareResponseDto> CheckPrinterPaperAsync()
        {
            return await Task.FromResult(new HardwareResponseDto
            {
                Success = true,
                Message = "Paper level checked",
                Data = new Dictionary<string, object>
                {
                    ["paperLevel"] = _hardwareStatus.Printer.PaperLevel,
                    ["needsRefill"] = _hardwareStatus.Printer.PaperLevel < 20
                }
            });
        }

        public async Task<HardwareResponseDto> ResetPrinterAsync()
        {
            _logger.LogInformation("Resetting printer");
            
            _hardwareStatus.Printer.Status = DeviceStatus.Busy;
            await Task.Delay(3000); // Simulate reset time
            
            _hardwareStatus.Printer.Status = DeviceStatus.Ready;
            _hardwareStatus.Printer.LastError = null;
            
            return new HardwareResponseDto
            {
                Success = true,
                Message = "Printer reset successfully"
            };
        }

        public async Task<ImageCaptureResponseDto> CaptureImageAsync(CaptureImageDto captureDto)
        {
            _logger.LogInformation("Capturing image for purpose: {Purpose}", captureDto.Purpose);
            
            if (!_hardwareStatus.Camera.Connected)
            {
                return new ImageCaptureResponseDto
                {
                    Success = false,
                    Message = "Camera is not connected"
                };
            }

            // Simulate image capture
            _hardwareStatus.Camera.Status = DeviceStatus.Capturing;
            await Task.Delay(1500); // Simulate capture time
            
            var imageUrl = $"/images/capture_{DateTime.UtcNow:yyyyMMdd_HHmmss}.jpg";
            
            _hardwareStatus.Camera.Status = DeviceStatus.Ready;
            _hardwareStatus.Camera.LastCapture = DateTime.UtcNow;
            _hardwareStatus.Camera.LastImagePath = imageUrl;

            return new ImageCaptureResponseDto
            {
                Success = true,
                ImageUrl = imageUrl,
                ImagePath = imageUrl,
                Timestamp = DateTime.UtcNow,
                Message = "Image captured successfully"
            };
        }

        public async Task<HardwareResponseDto> TestCameraAsync()
        {
            _logger.LogInformation("Testing camera");
            
            var testCapture = new CaptureImageDto
            {
                Purpose = "test"
            };
            
            var result = await CaptureImageAsync(testCapture);
            
            return new HardwareResponseDto
            {
                Success = result.Success,
                Message = result.Message,
                Data = new Dictionary<string, object>
                {
                    ["imageUrl"] = result.ImageUrl ?? "",
                    ["timestamp"] = result.Timestamp
                }
            };
        }

        public async Task<HardwareResponseDto> SetCameraResolutionAsync(string resolution)
        {
            _logger.LogInformation("Setting camera resolution to: {Resolution}", resolution);
            
            _hardwareStatus.Camera.Resolution = resolution;
            
            return await Task.FromResult(new HardwareResponseDto
            {
                Success = true,
                Message = $"Camera resolution set to {resolution}"
            });
        }

        public async Task<HardwareResponseDto> ResetCameraAsync()
        {
            _logger.LogInformation("Resetting camera");
            
            _hardwareStatus.Camera.Status = DeviceStatus.Busy;
            await Task.Delay(2000); // Simulate reset time
            
            _hardwareStatus.Camera.Status = DeviceStatus.Ready;
            
            return new HardwareResponseDto
            {
                Success = true,
                Message = "Camera reset successfully"
            };
        }

        public async Task<CardReadResponseDto> ReadCardAsync()
        {
            _logger.LogInformation("Reading card");
            
            if (!_hardwareStatus.CardReader.Connected)
            {
                return new CardReadResponseDto
                {
                    Success = false,
                    Message = "Card reader is not connected"
                };
            }

            // Simulate card reading
            _hardwareStatus.CardReader.Status = DeviceStatus.Reading;
            await Task.Delay(2000); // Simulate read time
            
            // Simulate card data
            var cardId = $"MP{new Random().Next(100000, 999999)}";
            var balance = (decimal)(new Random().NextDouble() * 100 + 10); // Random balance between 10-110
            
            _hardwareStatus.CardReader.Status = DeviceStatus.Ready;
            _hardwareStatus.CardReader.LastTransaction = DateTime.UtcNow;
            _hardwareStatus.CardReader.LastCardBalance = balance;
            _hardwareStatus.CardReader.LastCardId = cardId;

            return new CardReadResponseDto
            {
                Success = true,
                CardId = cardId,
                Balance = balance,
                CardType = "Macau Pass",
                ReadTime = DateTime.UtcNow,
                Message = "Card read successfully"
            };
        }

        public async Task<CardPaymentResponseDto> ProcessCardPaymentAsync(CardPaymentDto paymentDto)
        {
            _logger.LogInformation("Processing card payment for amount: {Amount}", paymentDto.Amount);
            
            if (!_hardwareStatus.CardReader.Connected)
            {
                return new CardPaymentResponseDto
                {
                    Success = false,
                    Message = "Card reader is not connected"
                };
            }

            // Simulate payment processing
            _hardwareStatus.CardReader.Status = DeviceStatus.Reading;
            await Task.Delay(3000); // Simulate processing time
            
            var currentBalance = _hardwareStatus.CardReader.LastCardBalance ?? 50m;
            
            if (currentBalance < paymentDto.Amount)
            {
                _hardwareStatus.CardReader.Status = DeviceStatus.Ready;
                return new CardPaymentResponseDto
                {
                    Success = false,
                    Message = "Insufficient funds on card"
                };
            }
            
            var newBalance = currentBalance - paymentDto.Amount;
            var transactionId = Guid.NewGuid().ToString();
            
            _hardwareStatus.CardReader.Status = DeviceStatus.Ready;
            _hardwareStatus.CardReader.LastTransaction = DateTime.UtcNow;
            _hardwareStatus.CardReader.LastCardBalance = newBalance;

            return new CardPaymentResponseDto
            {
                Success = true,
                TransactionId = transactionId,
                NewBalance = newBalance,
                AmountCharged = paymentDto.Amount,
                ProcessedAt = DateTime.UtcNow,
                Message = "Payment processed successfully"
            };
        }

        public async Task<HardwareResponseDto> TestCardReaderAsync()
        {
            _logger.LogInformation("Testing card reader");
            
            var result = await ReadCardAsync();
            
            return new HardwareResponseDto
            {
                Success = result.Success,
                Message = result.Message,
                Data = new Dictionary<string, object>
                {
                    ["cardId"] = result.CardId ?? "",
                    ["balance"] = result.Balance,
                    ["readTime"] = result.ReadTime
                }
            };
        }

        public async Task<HardwareResponseDto> ResetCardReaderAsync()
        {
            _logger.LogInformation("Resetting card reader");
            
            _hardwareStatus.CardReader.Status = DeviceStatus.Busy;
            await Task.Delay(2000); // Simulate reset time
            
            _hardwareStatus.CardReader.Status = DeviceStatus.Ready;
            
            return new HardwareResponseDto
            {
                Success = true,
                Message = "Card reader reset successfully"
            };
        }

        public async Task<VehicleDetectionResponseDto> CheckVehiclePresenceAsync()
        {
            // Simulate sensor reading
            var random = new Random();
            var detected = random.Next(0, 10) < 3; // 30% chance of detection
            
            _hardwareStatus.GroundSensor.VehicleDetected = detected;
            if (detected)
            {
                _hardwareStatus.GroundSensor.LastDetection = DateTime.UtcNow;
            }

            return await Task.FromResult(new VehicleDetectionResponseDto
            {
                VehicleDetected = detected,
                CheckTime = DateTime.UtcNow,
                SensorReading = random.Next(0, 100),
                Message = detected ? "Vehicle detected" : "No vehicle detected"
            });
        }

        public async Task<HardwareResponseDto> SetSensorSensitivityAsync(int sensitivity)
        {
            _logger.LogInformation("Setting ground sensor sensitivity to: {Sensitivity}", sensitivity);
            
            _hardwareStatus.GroundSensor.Sensitivity = sensitivity;
            
            return await Task.FromResult(new HardwareResponseDto
            {
                Success = true,
                Message = $"Ground sensor sensitivity set to {sensitivity}"
            });
        }

        public async Task<HardwareResponseDto> TestGroundSensorAsync()
        {
            _logger.LogInformation("Testing ground sensor");
            
            var result = await CheckVehiclePresenceAsync();
            
            return new HardwareResponseDto
            {
                Success = true,
                Message = result.Message,
                Data = new Dictionary<string, object>
                {
                    ["vehicleDetected"] = result.VehicleDetected,
                    ["sensorReading"] = result.SensorReading,
                    ["checkTime"] = result.CheckTime
                }
            };
        }

        public async Task<HardwareResponseDto> ResetGroundSensorAsync()
        {
            _logger.LogInformation("Resetting ground sensor");
            
            _hardwareStatus.GroundSensor.Status = DeviceStatus.Busy;
            await Task.Delay(1500); // Simulate reset time
            
            _hardwareStatus.GroundSensor.Status = _hardwareStatus.GroundSensor.Connected ? DeviceStatus.Ready : DeviceStatus.Offline;
            _hardwareStatus.GroundSensor.VehicleDetected = false;
            
            return new HardwareResponseDto
            {
                Success = true,
                Message = "Ground sensor reset successfully"
            };
        }

        public async Task<HardwareDiagnosticsDto> RunFullDiagnosticsAsync()
        {
            _logger.LogInformation("Running full hardware diagnostics");
            
            var startTime = DateTime.UtcNow;
            var results = new List<DiagnosticResult>();
            
            // Test each device
            await Task.Delay(5000); // Simulate comprehensive testing
            
            results.Add(new DiagnosticResult
            {
                DeviceType = "printer",
                TestName = "Connection Test",
                Passed = _hardwareStatus.Printer.Connected,
                Message = _hardwareStatus.Printer.Connected ? "Printer connected" : "Printer not connected"
            });
            
            results.Add(new DiagnosticResult
            {
                DeviceType = "camera",
                TestName = "Connection Test",
                Passed = _hardwareStatus.Camera.Connected,
                Message = _hardwareStatus.Camera.Connected ? "Camera connected" : "Camera not connected"
            });
            
            results.Add(new DiagnosticResult
            {
                DeviceType = "cardreader",
                TestName = "Connection Test",
                Passed = _hardwareStatus.CardReader.Connected,
                Message = _hardwareStatus.CardReader.Connected ? "Card reader connected" : "Card reader not connected"
            });
            
            results.Add(new DiagnosticResult
            {
                DeviceType = "groundsensor",
                TestName = "Connection Test",
                Passed = _hardwareStatus.GroundSensor.Connected,
                Message = _hardwareStatus.GroundSensor.Connected ? "Ground sensor connected" : "Ground sensor not connected"
            });
            
            var passedTests = results.Count(r => r.Passed);
            var overallHealth = passedTests == results.Count ? "excellent" : 
                               passedTests >= results.Count * 0.75 ? "good" : 
                               passedTests >= results.Count * 0.5 ? "fair" : "poor";
            
            return new HardwareDiagnosticsDto
            {
                Results = results,
                OverallHealth = overallHealth,
                RunTime = startTime,
                Duration = DateTime.UtcNow - startTime
            };
        }

        public async Task<HardwareResponseDto> ResetAllHardwareAsync()
        {
            _logger.LogInformation("Resetting all hardware");
            
            await Task.WhenAll(
                ResetPrinterAsync(),
                ResetCameraAsync(),
                ResetCardReaderAsync(),
                ResetGroundSensorAsync()
            );
            
            return new HardwareResponseDto
            {
                Success = true,
                Message = "All hardware reset successfully"
            };
        }

        public async Task<HardwareResponseDto> ExecuteHardwareCommandAsync(HardwareCommandDto commandDto)
        {
            _logger.LogInformation("Executing command {Command} on {DeviceType}", commandDto.Command, commandDto.DeviceType);
            
            // Route command to appropriate device handler
            return commandDto.DeviceType.ToLower() switch
            {
                "printer" => await HandlePrinterCommand(commandDto),
                "camera" => await HandleCameraCommand(commandDto),
                "cardreader" => await HandleCardReaderCommand(commandDto),
                "groundsensor" => await HandleGroundSensorCommand(commandDto),
                _ => new HardwareResponseDto
                {
                    Success = false,
                    Message = $"Unknown device type: {commandDto.DeviceType}"
                }
            };
        }

        private async Task<HardwareResponseDto> HandlePrinterCommand(HardwareCommandDto commandDto)
        {
            return commandDto.Command.ToLower() switch
            {
                "test" => await TestPrinterAsync(),
                "reset" => await ResetPrinterAsync(),
                _ => new HardwareResponseDto { Success = false, Message = $"Unknown printer command: {commandDto.Command}" }
            };
        }

        private async Task<HardwareResponseDto> HandleCameraCommand(HardwareCommandDto commandDto)
        {
            return commandDto.Command.ToLower() switch
            {
                "test" => await TestCameraAsync(),
                "reset" => await ResetCameraAsync(),
                _ => new HardwareResponseDto { Success = false, Message = $"Unknown camera command: {commandDto.Command}" }
            };
        }

        private async Task<HardwareResponseDto> HandleCardReaderCommand(HardwareCommandDto commandDto)
        {
            return commandDto.Command.ToLower() switch
            {
                "test" => await TestCardReaderAsync(),
                "reset" => await ResetCardReaderAsync(),
                _ => new HardwareResponseDto { Success = false, Message = $"Unknown card reader command: {commandDto.Command}" }
            };
        }

        private async Task<HardwareResponseDto> HandleGroundSensorCommand(HardwareCommandDto commandDto)
        {
            return commandDto.Command.ToLower() switch
            {
                "test" => await TestGroundSensorAsync(),
                "reset" => await ResetGroundSensorAsync(),
                _ => new HardwareResponseDto { Success = false, Message = $"Unknown ground sensor command: {commandDto.Command}" }
            };
        }

        public async Task<HardwareConfiguration> GetHardwareConfigurationAsync()
        {
            return await Task.FromResult(_hardwareConfig);
        }

        public async Task<HardwareConfiguration> UpdateHardwareConfigurationAsync(HardwareConfiguration config)
        {
            _hardwareConfig = config;
            return await Task.FromResult(_hardwareConfig);
        }

        public async Task<bool> IsHardwareHealthyAsync()
        {
            var deviceHealth = await GetDeviceHealthStatusAsync();
            return deviceHealth.Values.Count(healthy => healthy) >= deviceHealth.Count * 0.75; // 75% healthy threshold
        }

        public async Task<Dictionary<string, bool>> GetDeviceHealthStatusAsync()
        {
            return await Task.FromResult(new Dictionary<string, bool>
            {
                ["printer"] = _hardwareStatus.Printer.Connected && _hardwareStatus.Printer.Status != DeviceStatus.Error,
                ["camera"] = _hardwareStatus.Camera.Connected && _hardwareStatus.Camera.Status != DeviceStatus.Error,
                ["cardreader"] = _hardwareStatus.CardReader.Connected && _hardwareStatus.CardReader.Status != DeviceStatus.Error,
                ["groundsensor"] = _hardwareStatus.GroundSensor.Connected && _hardwareStatus.GroundSensor.Status != DeviceStatus.Error
            });
        }

        public async Task StartHealthMonitoringAsync()
        {
            _logger.LogInformation("Starting hardware health monitoring");
            // Implementation would start background monitoring
            await Task.CompletedTask;
        }

        public async Task StopHealthMonitoringAsync()
        {
            _logger.LogInformation("Stopping hardware health monitoring");
            // Implementation would stop background monitoring
            await Task.CompletedTask;
        }
    }
}
