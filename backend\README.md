# Parking Booth API

A .NET 7 Web API backend for the parking booth management system, providing RESTful endpoints for parking operations and hardware integration.

## Features

- **Parking Operations**: Vehicle entry/exit management and fee calculation
- **Payment Processing**: Support for cash, credit card, and Macau Pass payments
- **Hardware Integration**: Control and monitor printer, camera, card reader, and ground sensors
- **Real-time Status**: Hardware status monitoring and diagnostics
- **Configuration Management**: Parking rates, rules, and hardware settings
- **Statistics & Reporting**: Daily, weekly, and monthly parking reports

## Technology Stack

- **.NET 7** Web API
- **ASP.NET Core** for web framework
- **Swagger/OpenAPI** for API documentation
- **CORS** enabled for frontend communication
- **Dependency Injection** for service management
- **Structured Logging** with ILogger

## Project Structure

```
├── Controllers/            # API controllers
│   ├── ParkingController.cs
│   └── HardwareController.cs
├── Services/              # Business logic services
│   ├── ParkingService.cs
│   └── HardwareService.cs
├── Interfaces/            # Service interfaces
│   ├── IParkingService.cs
│   └── IHardwareService.cs
├── Models/                # Domain models
│   ├── Vehicle.cs
│   ├── ParkingTransaction.cs
│   ├── HardwareStatus.cs
│   └── ParkingConfiguration.cs
├── DTOs/                  # Data Transfer Objects
│   ├── VehicleDto.cs
│   ├── ParkingTransactionDto.cs
│   └── HardwareDto.cs
├── Program.cs             # Application entry point
└── appsettings.json       # Configuration
```

## API Endpoints

### Parking Operations
- `POST /api/parking/entry` - Record vehicle entry
- `POST /api/parking/exit` - Record vehicle exit
- `GET /api/parking/current` - Get current vehicles
- `GET /api/parking/search` - Search vehicle by license plate
- `POST /api/parking/payment` - Process payment
- `GET /api/parking/calculate-fee/{vehicleId}` - Calculate parking fee
- `GET /api/parking/transactions` - Get transactions
- `GET /api/parking/transactions/pending` - Get pending payments

### Statistics & Reports
- `GET /api/parking/stats/today` - Today's statistics
- `GET /api/parking/stats/historical` - Historical statistics
- `GET /api/parking/reports/{type}` - Generate reports

### Configuration
- `GET /api/parking/config` - Get parking configuration
- `PUT /api/parking/config` - Update parking configuration

### Hardware Control
- `GET /api/hardware/status` - Get all hardware status
- `POST /api/hardware/status/refresh` - Refresh hardware status

#### Printer
- `GET /api/hardware/printer/status` - Get printer status
- `POST /api/hardware/printer/print` - Print ticket
- `POST /api/hardware/printer/test` - Test printer
- `POST /api/hardware/printer/reset` - Reset printer

#### Camera
- `GET /api/hardware/camera/status` - Get camera status
- `POST /api/hardware/camera/capture` - Capture image
- `POST /api/hardware/camera/test` - Test camera
- `POST /api/hardware/camera/reset` - Reset camera

#### Card Reader
- `GET /api/hardware/cardreader/status` - Get card reader status
- `POST /api/hardware/cardreader/read` - Read card
- `POST /api/hardware/cardreader/payment` - Process card payment
- `POST /api/hardware/cardreader/test` - Test card reader
- `POST /api/hardware/cardreader/reset` - Reset card reader

#### Ground Sensor
- `GET /api/hardware/groundsensor/status` - Get ground sensor status
- `GET /api/hardware/groundsensor/detect` - Check vehicle presence
- `POST /api/hardware/groundsensor/test` - Test ground sensor
- `POST /api/hardware/groundsensor/reset` - Reset ground sensor

#### Diagnostics
- `POST /api/hardware/diagnostics` - Run full diagnostics
- `POST /api/hardware/command` - Execute custom hardware command

### Health Check
- `GET /api/health` - API health status

## Hardware Integration

The API provides interfaces for hardware components commonly found in parking booth systems:

### Printer Interface
- Thermal receipt/ticket printing
- Paper level monitoring
- Error handling and recovery
- Test printing capabilities

### Camera Interface
- Image capture for license plate recognition
- Configurable resolution settings
- Automatic capture on vehicle entry/exit
- Image storage and retrieval

### Macau Pass Card Reader Interface
- Card reading and validation
- Balance checking
- Payment processing
- Transaction logging

### Ground Sensor Interface
- Vehicle presence detection
- Configurable sensitivity settings
- Real-time monitoring
- Integration with entry/exit workflows

## Configuration

### Application Settings
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ParkingBooth": {
    "BoothId": "001",
    "MaxCapacity": 100,
    "DefaultRates": {
      "Car": 2.0,
      "Motorcycle": 1.0,
      "Truck": 3.0
    }
  }
}
```

### CORS Configuration
The API is configured to allow requests from:
- `http://localhost:5173` (Vue.js dev server)
- `http://localhost:3000` (Alternative frontend port)

## Development Setup

### Prerequisites
- .NET 7 SDK
- Visual Studio 2022 or VS Code

### Running the API
```bash
# Restore dependencies
dotnet restore

# Run the application
dotnet run

# Run with hot reload
dotnet watch run
```

The API will be available at:
- HTTP: `http://localhost:5000`
- HTTPS: `https://localhost:5001`
- Swagger UI: `http://localhost:5000/swagger`

### Building for Production
```bash
# Build the application
dotnet build --configuration Release

# Publish for deployment
dotnet publish --configuration Release --output ./publish
```

## Data Models

### Vehicle
- License plate, entry/exit times, vehicle type
- Parking spot assignment, image capture
- Status tracking (parked, exited, payment pending)

### Parking Transaction
- Fee calculation, payment processing
- Duration tracking, payment method
- Transaction status and references

### Hardware Status
- Real-time device status monitoring
- Connection status, error tracking
- Performance metrics and diagnostics

## Service Architecture

### Parking Service
- Vehicle entry/exit management
- Fee calculation and payment processing
- Statistics generation and reporting
- Configuration management

### Hardware Service
- Device status monitoring and control
- Hardware command execution
- Diagnostics and maintenance operations
- Error handling and recovery

## Error Handling

- Structured error responses with appropriate HTTP status codes
- Comprehensive logging for debugging and monitoring
- Hardware failure detection and recovery
- Input validation and business rule enforcement

## Security Considerations

- Input validation on all endpoints
- CORS configuration for frontend access
- Structured logging without sensitive data
- Error messages that don't expose internal details

## Testing

```bash
# Run unit tests (when implemented)
dotnet test

# Run with coverage (when implemented)
dotnet test --collect:"XPlat Code Coverage"
```

## Deployment

### Docker Deployment
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["ParkingBoothApi.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ParkingBoothApi.dll"]
```

### IIS Deployment
1. Publish the application
2. Copy to IIS wwwroot
3. Configure application pool for .NET 7
4. Set up HTTPS certificates

## Monitoring and Logging

- Structured logging with Serilog (can be added)
- Health check endpoints for monitoring
- Performance counters and metrics
- Hardware status monitoring

## Contributing

1. Follow .NET coding conventions
2. Add XML documentation for public APIs
3. Include unit tests for new features
4. Update API documentation
5. Test hardware integration thoroughly

## License

[Add your license information here]
